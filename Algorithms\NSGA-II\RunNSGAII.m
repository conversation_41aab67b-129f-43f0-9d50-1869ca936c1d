function [population, objectives, convergence_history] = RunNSGAII(problem, params)
% RunNSGAII - 运行NSGA-II算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%   convergence_history - 收敛历史记录，包含每代的最小总质量
%
% 参考文献:
% <PERSON><PERSON>, <PERSON><PERSON>, S. <PERSON>, and <PERSON><PERSON>, A fast and elitist
% multiobjective genetic algorithm: NSGA-II, IEEE Transactions on
% Evolutionary Computation, 2002, 6(2): 182-197.

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 初始化种群
empty_individual.Position = [];
empty_individual.Cost = [];
empty_individual.Rank = [];
empty_individual.DominationSet = [];
empty_individual.DominatedCount = [];
empty_individual.CrowdingDistance = [];

pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    % 生成随机解
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    % 评估目标函数
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 非支配排序
[pop, F] = NonDominatedSorting(pop);

% 计算拥挤度距离
pop = CalcCrowdingDistance(pop, F);

% 排序种群
pop = SortPopulation(pop);

% 初始化收敛历史记录
convergence_history = struct();
convergence_history.iteration = zeros(maxIt + 1, 1);
convergence_history.min_mass = zeros(maxIt + 1, 1);
convergence_history.algorithm_name = 'NSGA-II';

% 记录初始代的最小总质量（纯齿轮质量，不包含惩罚项）
% 需要先进行非支配排序以获得第一前沿
[pop_temp, F_temp] = NonDominatedSorting(pop);
current_costs = vertcat(pop_temp.Cost);
valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
if ~isempty(valid_costs) && ~isempty(F_temp{1})
    % 计算非支配解集中满足工程约束的最小纯齿轮质量
    min_pure_mass = inf;
    min_pure_mass_all = inf;  % 所有解中的最小质量（备用）

    for idx = F_temp{1}  % 只考虑第一前沿（非支配解集）
        if idx <= length(pop_temp) && ~any(isnan(pop_temp(idx).Cost) | isinf(pop_temp(idx).Cost))
            try
                % 重新评估这个解以获取纯齿轮质量
                pure_mass = calculatePureGearMass(pop_temp(idx).Position, problem);

                % 记录所有解中的最小质量
                if pure_mass < min_pure_mass_all
                    min_pure_mass_all = pure_mass;
                end

                % 检查是否满足工程约束（与CSV表格筛选逻辑一致）
                objectives = pop_temp(idx).Cost;
                if length(objectives) >= 3
                    % 目标函数：[总质量, -弯曲安全系数, -接触安全系数]
                    bending_sf = -objectives(2);
                    contact_sf = -objectives(3);

                    % 检查安全系数约束（使用系统参数中的要求）
                    min_contact_safety = problem.system_params.contact_safety_factor;
                    min_bending_safety = problem.system_params.bending_safety_factor;

                    % 调试信息：在最后一次迭代时输出每个解的详细信息
                    % if it == maxIt
                    %     fprintf('  解%d: 质量=%.2f, 接触SF=%.3f(要求%.3f), 弯曲SF=%.3f(要求%.3f), 满足约束=%s\n', ...
                    %             idx, pure_mass, contact_sf, min_contact_safety, bending_sf, min_bending_safety, ...
                    %             char(string(contact_sf >= min_contact_safety && bending_sf >= min_bending_safety)));
                    % end

                    % 计算传动比误差（与CSV表格生成逻辑一致）
                    x_current = pop(idx).Position;
                    target_ratio = problem.system_params.input_speed / problem.system_params.output_speed;

                    % 计算实际传动比
                    try
                        % 获取一级参数
                        first_stage_idx = round(x_current(1));
                        first_stage_idx = max(1, min(first_stage_idx, height(problem.first_stage_params)));
                        first_stage_values = table2array(problem.first_stage_params(first_stage_idx, 1:7));
                        i1 = first_stage_values(4);  % 一级传动比

                        % 计算二三级传动比
                        zs2 = round(x_current(3)); zr2 = round(x_current(4));
                        zs3 = round(x_current(7)); zr3 = round(x_current(8));
                        i2 = 1 + zr2 / zs2;  % 二级传动比
                        i3 = 1 + zr3 / zs3;  % 三级传动比

                        actual_ratio = i1 * i2 * i3;
                        ratio_error = abs(actual_ratio - target_ratio) / target_ratio * 100;
                    catch
                        ratio_error = 100;  % 计算失败时设为大值
                    end

                    % 检查是否同时满足安全系数和传动比要求（与CSV表格筛选逻辑完全一致）
                    max_ratio_error = 2.0;  % 最大传动比误差百分比

                    if contact_sf >= min_contact_safety && bending_sf >= min_bending_safety && ratio_error <= max_ratio_error
                        % 同时满足安全系数和传动比要求的解
                        if pure_mass < min_pure_mass
                            min_pure_mass = pure_mass;
                        end
                    end
                end
            catch
                % 如果计算失败，跳过这个解
                continue;
            end
        end
    end

    % 如果有满足约束的解，使用其最小质量；否则使用所有解中的最小质量
    if min_pure_mass < inf
        convergence_history.min_mass(1) = min_pure_mass;
    else
        convergence_history.min_mass(1) = min_pure_mass_all;
    end
else
    convergence_history.min_mass(1) = inf;
end
convergence_history.iteration(1) = 0;

%% 优化主循环
for it = 1:maxIt
    % 交叉
    popc = repmat(empty_individual, nPop, 1);
    for i = 1:nPop/2
        % 锦标赛选择
        p1 = TournamentSelection(pop);
        p2 = TournamentSelection(pop);
        
        % 交叉
        [popc(2*i-1).Position, popc(2*i).Position] = Crossover(p1.Position, p2.Position, pCrossover, varMin, varMax);
        
        % 变异
        popc(2*i-1).Position = Mutate(popc(2*i-1).Position, pMutation, varMin, varMax);
        popc(2*i).Position = Mutate(popc(2*i).Position, pMutation, varMin, varMax);
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for k = [2*i-1, 2*i]
                for j = 1:length(problem.discreteVars)
                    idx = problem.discreteVars(j).idx;
                    if problem.discreteVars(j).isInteger
                        popc(k).Position(idx) = round(popc(k).Position(idx));
                    else
                        % 找到最接近的离散值
                        values = problem.discreteVars(j).values;
                        [~, closest_idx] = min(abs(popc(k).Position(idx) - values));
                        popc(k).Position(idx) = values(closest_idx);
                    end
                end
            end
        end
        
        % 评估子代
        popc(2*i-1).Cost = problem.costFunction(popc(2*i-1).Position);
        popc(2*i).Cost = problem.costFunction(popc(2*i).Position);
        problem.FE = problem.FE + 2;
    end
    
    % 合并父代和子代
    pop = [pop; popc];
    
    % 非支配排序
    [pop, F] = NonDominatedSorting(pop);
    
    % 计算拥挤度距离
    pop = CalcCrowdingDistance(pop, F);
    
    % 精英选择
    pop = EliteSelection(pop, nPop);

    % 记录当前迭代的最小总质量（纯齿轮质量，不包含惩罚项）
    % 应用与CSV表格相同的筛选逻辑：优先选择满足工程约束的解
    current_costs = vertcat(pop.Cost);
    valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
    if ~isempty(valid_costs) && ~isempty(F{1})
        % 计算非支配解集中满足工程约束的最小纯齿轮质量
        min_pure_mass = inf;
        min_pure_mass_all = inf;  % 所有解中的最小质量（备用）

        for idx = F{1}  % 只考虑第一前沿（非支配解集）
            if idx <= length(pop) && ~any(isnan(pop(idx).Cost) | isinf(pop(idx).Cost))
                try
                    % 重新评估这个解以获取纯齿轮质量
                    pure_mass = calculatePureGearMass(pop(idx).Position, problem);

                    % 记录所有解中的最小质量
                    if pure_mass < min_pure_mass_all
                        min_pure_mass_all = pure_mass;
                    end

                    % 检查是否满足工程约束（与CSV表格筛选逻辑一致）
                    objectives = pop(idx).Cost;
                    if length(objectives) >= 3
                        % 目标函数：[总质量, -弯曲安全系数, -接触安全系数]
                        bending_sf = -objectives(2);
                        contact_sf = -objectives(3);

                        % 检查安全系数约束（使用系统参数中的要求）
                        min_contact_safety = problem.system_params.contact_safety_factor;
                        min_bending_safety = problem.system_params.bending_safety_factor;

                        % 计算传动比误差（与CSV表格生成逻辑一致）
                        x_current = pop(idx).Position;
                        target_ratio = problem.system_params.input_speed / problem.system_params.output_speed;

                        % 计算实际传动比
                        try
                            % 获取一级参数
                            first_stage_idx = round(x_current(1));
                            first_stage_idx = max(1, min(first_stage_idx, height(problem.first_stage_params)));
                            first_stage_values = table2array(problem.first_stage_params(first_stage_idx, 1:7));
                            i1 = first_stage_values(4);  % 一级传动比

                            % 计算二三级传动比
                            zs2 = round(x_current(3)); zr2 = round(x_current(4));
                            zs3 = round(x_current(7)); zr3 = round(x_current(8));
                            i2 = 1 + zr2 / zs2;  % 二级传动比
                            i3 = 1 + zr3 / zs3;  % 三级传动比

                            actual_ratio = i1 * i2 * i3;
                            ratio_error = abs(actual_ratio - target_ratio) / target_ratio * 100;
                        catch
                            ratio_error = 100;  % 计算失败时设为大值
                        end

                        % 检查是否同时满足安全系数和传动比要求（与CSV表格筛选逻辑完全一致）
                        max_ratio_error = 2.0;  % 最大传动比误差百分比

                        if contact_sf >= min_contact_safety && bending_sf >= min_bending_safety && ratio_error <= max_ratio_error
                            % 同时满足安全系数和传动比要求的解
                            if pure_mass < min_pure_mass
                                min_pure_mass = pure_mass;
                            end
                        end
                    end
                catch
                    % 如果计算失败，跳过这个解
                    continue;
                end
            end
        end

        % 如果有满足约束的解，使用其最小质量；否则使用所有解中的最小质量
        if min_pure_mass < inf
            convergence_history.min_mass(it + 1) = min_pure_mass;

        else
            convergence_history.min_mass(it + 1) = min_pure_mass_all;
            % 调试信息：在最后一次迭代时输出详细信息
            if it == maxIt
                fprintf('最后一次迭代调试信息：\n');
                fprintf('  非支配解数量: %d\n', length(F{1}));
                fprintf('  没有满足约束的解，使用所有解中的最小质量: %.2f kg\n', min_pure_mass_all);
            end
        end
    else
        convergence_history.min_mass(it + 1) = inf;
    end
    convergence_history.iteration(it + 1) = it;

    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        % 计算非支配解数量
        n_nondom = numel(F{1});
        % 显示纯齿轮质量（不包含惩罚项）
        display_mass = convergence_history.min_mass(it + 1);
        if display_mass == inf || display_mass >= 1e6
            disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE) ', 最小总质量 = 无效']);
        else
            disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE) ', 最小总质量 = ' num2str(display_mass, '%.2f')]);
        end
    end
end

% 再次对最终种群进行非支配排序
[pop, F] = NonDominatedSorting(pop);

% 提取非支配解集
% 确保F{1}中的索引不超出pop的范围
valid_indices = F{1}(F{1} <= length(pop));
if isempty(valid_indices)
    % 如果没有有效的非支配解，使用整个种群
    nonDominated = pop;
else
    nonDominated = pop(valid_indices);
end

% 返回最终结果
n_pareto = numel(nonDominated);
population = zeros(n_pareto, nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = nonDominated(i).Position;
    objectives(i, :) = nonDominated(i).Cost;

    % 重新计算纯齿轮质量，替换包含惩罚项的目标函数值
    try
        pure_mass = calculatePureGearMass(nonDominated(i).Position, problem);
        objectives(i, 1) = pure_mass;  % 用纯齿轮质量替换包含惩罚项的目标函数值
    catch
        % 如果计算失败，保持原值
    end
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数



% 裁剪收敛历史记录到实际使用的长度
convergence_history.iteration = convergence_history.iteration(1:maxIt+1);
convergence_history.min_mass = convergence_history.min_mass(1:maxIt+1);

% CSV表格将在算法完成后由主程序生成，此时无法读取

% 收敛历史记录保持原始目标函数值（包含惩罚项）
% 不进行任何转换，保持数据的真实性

% 不再输出算法完成信息，由主程序统一处理
end

function x_corrected = applyCenterDistanceConstraints(x)
% 严格约束二三级行星轮系的中心距
% 二级中心距：300-350mm，三级中心距：350-400mm

x_corrected = x;

if length(x) >= 8
    % 提取参数
    mn2 = x(2);    % 二级模数
    zs2 = round(x(3));  % 二级太阳轮齿数
    zr2 = round(x(4));  % 二级内齿圈齿数

    % 确保内齿圈齿数和太阳轮齿数的差是偶数
    if mod(zr2 - zs2, 2) ~= 0
        zr2 = zr2 + 1;  % 调整内齿圈齿数使其为偶数
        x_corrected(4) = zr2;
    end

    zp2 = (zr2 - zs2) / 2;  % 二级行星轮齿数（计算得出）
    mn3 = x(6);    % 三级模数
    zs3 = round(x(7));  % 三级太阳轮齿数
    zr3 = round(x(8));  % 三级内齿圈齿数

    % 确保内齿圈齿数和太阳轮齿数的差是偶数
    if mod(zr3 - zs3, 2) ~= 0
        zr3 = zr3 + 1;  % 调整内齿圈齿数使其为偶数
        x_corrected(8) = zr3;
    end

    zp3 = (zr3 - zs3) / 2;  % 三级行星轮齿数（计算得出）

    % 二级中心距约束修正 (300-350mm) - 考虑变位系数的影响
    % 估算变位系数对中心距的影响（保守估计）
    estimated_shift_effect = 1.02;  % 更保守的估计，二级变位系数影响约2%

    % 计算考虑变位系数影响的估算中心距
    a2_estimated = mn2 * (zs2 + zp2) / 2 * estimated_shift_effect;

    if a2_estimated < 300 || a2_estimated > 350
        % 优先调整模数来满足中心距约束
        target_a2 = 320;  % 目标中心距（偏小的安全值）
        % 考虑变位系数影响的目标标准中心距
        target_standard_a2 = target_a2 / estimated_shift_effect;
        required_mn2 = 2 * target_standard_a2 / (zs2 + zp2);
        new_mn2 = max(7, min(15, round(required_mn2)));
        x_corrected(2) = new_mn2;
    end

    % 三级中心距约束修正 (350-400mm) - 考虑变位系数的影响
    % 估算变位系数对中心距的影响（保守估计）
    % 由于此时变位系数可能还未确定，使用经验值估算
    estimated_shift_effect = 1.03;  % 更保守的估计，变位系数可能使中心距增加3%

    % 计算考虑变位系数影响的估算中心距
    a3_estimated = mn3 * (zs3 + zp3) / 2 * estimated_shift_effect;

    if a3_estimated < 350 || a3_estimated > 400
        % 优先调整模数来满足中心距约束
        target_a3 = 365;  % 目标中心距（更保守的安全值）
        % 考虑变位系数影响的目标标准中心距
        target_standard_a3 = target_a3 / estimated_shift_effect;
        required_mn3 = 2 * target_standard_a3 / (zs3 + zp3);
        new_mn3 = max(12, min(20, round(required_mn3)));
        x_corrected(6) = new_mn3;
    end
end

end

function pure_mass = calculatePureGearMass(x, problem)
% calculatePureGearMass 计算纯齿轮质量（不包含惩罚项）
% 按照CSV表格中的计算方法：M1 + M2 + Ms2 + Mp2 + Mr2 + Ms3 + Mp3 + Mr3
%
% 输入:
%   x - 优化变量向量
%   problem - 问题结构体，包含system_params和first_stage_params
%
% 输出:
%   pure_mass - 纯齿轮质量 (kg)

try
    % 获取系统参数
    system_params = problem.system_params;
    first_stage_params = problem.first_stage_params;

    % 确保x是行向量
    if size(x, 1) > size(x, 2)
        x = x';
    end

    % 应用与CSV表格生成时相同的约束修正
    x = applyCenterDistanceConstraints(x);

    % 第一个变量是一级参数组索引
    first_stage_index = round(x(1));
    first_stage_index = max(1, min(first_stage_index, height(first_stage_params)));

    % 一级质量计算（从参数表获取实际值）
    if ismember('小齿轮质量(kg)', first_stage_params.Properties.VariableNames) && ...
       ismember('大齿轮质量(kg)', first_stage_params.Properties.VariableNames)
        M1 = first_stage_params{first_stage_index, '小齿轮质量(kg)'};
        M2 = first_stage_params{first_stage_index, '大齿轮质量(kg)'};
        first_stage_mass = M1 + M2;
    else
        error('一级参数表缺少质量数据');
    end

    % 提取二三级参数（按照主程序中的正确索引）
    mn2 = round(x(2));      % 二级模数（离散值）
    zs2 = round(x(3));      % 二级太阳轮齿数
    zr2 = round(x(4));      % 二级内齿圈齿数

    % 确保内齿圈齿数和太阳轮齿数的差是偶数
    if mod(zr2 - zs2, 2) ~= 0
        zr2 = zr2 + 1;  % 调整内齿圈齿数使其为偶数
    end

    zp2 = (zr2 - zs2) / 2;  % 二级行星轮齿数（计算得出）
    k_h2 = x(5);            % 二级齿宽系数
    mn3 = round(x(6));      % 三级模数（离散值）
    zs3 = round(x(7));      % 三级太阳轮齿数
    zr3 = round(x(8));      % 三级内齿圈齿数

    % 确保内齿圈齿数和太阳轮齿数的差是偶数
    if mod(zr3 - zs3, 2) ~= 0
        zr3 = zr3 + 1;  % 调整内齿圈齿数使其为偶数
    end

    zp3 = (zr3 - zs3) / 2;  % 三级行星轮齿数（计算得出）
    k_h3 = x(9);            % 三级齿宽系数
    n2 = round(x(10));      % 二级行星轮数量
    n3 = round(x(11));      % 三级行星轮数量

    % 提取变位系数（19个变量的完整索引）
    xs2 = x(15);  % 二级太阳轮变位系数
    xp2 = x(16);  % 二级行星轮变位系数
    xs3 = x(17);  % 三级太阳轮变位系数
    xp3 = x(18);  % 三级行星轮变位系数

    % 二级质量计算
    stage2_params = struct();
    stage2_params.mn = mn2;
    stage2_params.z1 = zs2;
    stage2_params.z2 = zp2;
    stage2_params.zr = zr2;
    stage2_params.b = mn2 * (zs2 + zp2) / 2 * k_h2;  % 按照主程序的计算方法
    stage2_params.planets_count = n2;
    stage2_params.xs = xs2;
    stage2_params.xp = xp2;
    stage2_params.xr = -xs2 - xp2;  % 与CSV表格生成时完全一致的计算方法
    stage2_params.stage = 2;

    [ms2, mp2, mr2, ~] = PlanetaryGearMassCalculator(stage2_params);
    second_stage_mass = ms2 + mp2 + mr2;

    % 三级质量计算
    stage3_params = struct();
    stage3_params.mn = mn3;
    stage3_params.z1 = zs3;
    stage3_params.z2 = zp3;
    stage3_params.zr = zr3;
    stage3_params.b = mn3 * (zs3 + zp3) / 2 * k_h3;  % 按照主程序的计算方法
    stage3_params.planets_count = n3;
    stage3_params.xs = xs3;
    stage3_params.xp = xp3;
    stage3_params.xr = -xs3 - xp3;  % 与CSV表格生成时完全一致的计算方法
    stage3_params.stage = 3;

    [ms3, mp3, mr3, ~] = PlanetaryGearMassCalculator(stage3_params);
    third_stage_mass = ms3 + mp3 + mr3;

    % 计算纯齿轮质量（按照CSV表格的计算方法）
    pure_mass = first_stage_mass + second_stage_mass + third_stage_mass;



catch ME
    % 如果计算失败，返回一个大值但不是无穷大
    pure_mass = 1e6;
end

end

%% 辅助函数
function [pop, F] = NonDominatedSorting(pop)
% 非支配排序

    nPop = numel(pop);
    
    % 初始化
    for i = 1:nPop
        pop(i).DominationSet = [];
        pop(i).DominatedCount = 0;
    end
    
    F{1} = [];
    
    % 计算支配关系
    for i = 1:nPop
        for j = i+1:nPop
            p = pop(i);
            q = pop(j);
            
            % 检查目标函数值是否有效
            p_valid = ~any(isnan(p.Cost) | isinf(p.Cost));
            q_valid = ~any(isnan(q.Cost) | isinf(q.Cost));
            
            if p_valid && q_valid
                if Dominates(p.Cost, q.Cost)
                    p.DominationSet = [p.DominationSet j];
                    q.DominatedCount = q.DominatedCount + 1;
                elseif Dominates(q.Cost, p.Cost)
                    q.DominationSet = [q.DominationSet i];
                    p.DominatedCount = p.DominatedCount + 1;
                end
            elseif p_valid && ~q_valid
                % 如果p有效而q无效，则p支配q
                p.DominationSet = [p.DominationSet j];
                q.DominatedCount = q.DominatedCount + 1;
            elseif ~p_valid && q_valid
                % 如果q有效而p无效，则q支配p
                q.DominationSet = [q.DominationSet i];
                p.DominatedCount = p.DominatedCount + 1;
            end
            
            pop(i) = p;
            pop(j) = q;
        end
        
        if pop(i).DominatedCount == 0
            F{1} = [F{1} i];
        end
    end
    
    % 如果第一个前沿为空，至少添加一个个体
    if isempty(F{1})
        % 找出有效的个体
        valid_indices = [];
        for i = 1:nPop
            if ~any(isnan(pop(i).Cost) | isinf(pop(i).Cost))
                valid_indices = [valid_indices i];
            end
        end
        
        % 如果有有效个体，选择第一个作为非支配解
        if ~isempty(valid_indices)
            F{1} = [F{1} valid_indices(1)];
        else
            % 如果没有有效个体，选择第一个个体
            F{1} = 1;
        end
    end
    
    k = 1;
    while true
        Q = [];
        for i = F{k}
            p = pop(i);
            for j = p.DominationSet
                pop(j).DominatedCount = pop(j).DominatedCount - 1;
                if pop(j).DominatedCount == 0
                    Q = [Q j];
                end
            end
        end
        
        if isempty(Q)
            break;
        end
        
        F{k+1} = Q;
        k = k + 1;
    end
    
    % 确保所有个体都被分配到某个前沿
    assigned = [];
    for k = 1:length(F)
        assigned = [assigned F{k}];
    end
    
    unassigned = setdiff(1:nPop, assigned);
    if ~isempty(unassigned)
        % 将未分配的个体添加到最后一个前沿
        F{end} = [F{end} unassigned];
    end
    
    % 设置每个个体的排名
    for k = 1:length(F)
        for i = F{k}
            pop(i).Rank = k;
        end
    end
end

function pop = CalcCrowdingDistance(pop, F)
% 计算拥挤度距离

    nF = numel(F);
    
    for k = 1:nF
        Fk = F{k};
        n = numel(Fk);
        
        if n <= 2
            for i = 1:n
                if i <= length(Fk)
                    pop(Fk(i)).CrowdingDistance = inf;
                end
            end
            continue;
        end
        
        for i = 1:n
            if i <= length(Fk)
                pop(Fk(i)).CrowdingDistance = 0;
            end
        end
        
        nObj = numel(pop(1).Cost);
        for j = 1:nObj
            % 提取当前前沿中所有个体的第j个目标值
            costs = zeros(n, 1);
            for i = 1:n
                if i <= length(Fk)
                    costs(i) = pop(Fk(i)).Cost(j);
                end
            end
            
            % 安全地排序
            [~, SO] = sort(costs);
            
            % 设置边界点的拥挤度为无穷大
            if 1 <= length(SO) && SO(1) <= length(Fk)
                pop(Fk(SO(1))).CrowdingDistance = inf;
            end
            
            if n <= length(SO) && SO(n) <= length(Fk)
                pop(Fk(SO(n))).CrowdingDistance = inf;
            end
            
            % 获取最大和最小值
            if n > 0 && 1 <= length(SO) && SO(1) <= length(Fk) && n <= length(SO) && SO(n) <= length(Fk)
                fmin = pop(Fk(SO(1))).Cost(j);
                fmax = pop(Fk(SO(n))).Cost(j);
                df = fmax - fmin;
                
                % 计算中间点的拥挤度
                if df > 0
                    for i = 2:n-1
                        if i <= length(SO) && SO(i) <= length(Fk) && SO(i+1) <= length(Fk) && SO(i-1) <= length(Fk)
                            pop(Fk(SO(i))).CrowdingDistance = pop(Fk(SO(i))).CrowdingDistance + ...
                                (pop(Fk(SO(i+1))).Cost(j) - pop(Fk(SO(i-1))).Cost(j))/df;
                        end
                    end
                end
            end
        end
    end
end

function pop = SortPopulation(pop)
% 根据排名和拥挤度距离排序种群

    % 获取排名
    ranks = [pop.Rank];
    
    % 获取拥挤度距离
    crowding_distances = [pop.CrowdingDistance];
    
    % 创建排序指标：首先按排名，然后按拥挤度距离（降序）
    [~, indices] = sortrows([ranks', -crowding_distances']);
    
    % 重新排序种群
    pop = pop(indices);
end

function p = TournamentSelection(pop)
% 锦标赛选择

    n = numel(pop);
    
    % 随机选择两个个体
    i1 = randi(n);
    i2 = randi(n);
    
    % 比较它们的排名
    if pop(i1).Rank < pop(i2).Rank
        p = pop(i1);
    elseif pop(i2).Rank < pop(i1).Rank
        p = pop(i2);
    else
        % 如果排名相同，选择拥挤度距离更大的
        if pop(i1).CrowdingDistance > pop(i2).CrowdingDistance
            p = pop(i1);
        else
            p = pop(i2);
        end
    end
end

function [y1, y2] = Crossover(x1, x2, pc, lb, ub)
% SBX交叉

    n = length(x1);
    y1 = x1;
    y2 = x2;
    
    if rand <= pc
        eta_c = 15;  % 交叉分布指数
        
        for j = 1:n
            if rand <= 0.5
                if abs(x1(j) - x2(j)) > 1e-10
                    if x1(j) < x2(j)
                        xl = x1(j);
                        xu = x2(j);
                    else
                        xl = x2(j);
                        xu = x1(j);
                    end
                    
                    beta = 1 + 2*(xl-lb(min(j,length(lb))))/(xu-xl);
                    alpha = 2 - beta^(-eta_c-1);
                    
                    if rand <= 1/alpha
                        beta_q = (rand*alpha)^(1/(eta_c+1));
                    else
                        beta_q = (1/(2-rand*alpha))^(1/(eta_c+1));
                    end
                    
                    c1 = 0.5*((xl+xu) - beta_q*(xu-xl));
                    
                    beta = 1 + 2*(ub(min(j,length(ub)))-xu)/(xu-xl);
                    alpha = 2 - beta^(-eta_c-1);
                    
                    if rand <= 1/alpha
                        beta_q = (rand*alpha)^(1/(eta_c+1));
                    else
                        beta_q = (1/(2-rand*alpha))^(1/(eta_c+1));
                    end
                    
                    c2 = 0.5*((xl+xu) + beta_q*(xu-xl));
                    
                    c1 = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), c1));
                    c2 = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), c2));
                    
                    if rand <= 0.5
                        y1(j) = c1;
                        y2(j) = c2;
                    else
                        y1(j) = c2;
                        y2(j) = c1;
                    end
                end
            end
        end
    end
end

function y = Mutate(x, pm, lb, ub)
% 多项式变异

    n = length(x);
    y = x;
    
    eta_m = 20;  % 变异分布指数
    
    for j = 1:n
        if rand <= pm
            delta1 = (y(j) - lb(min(j,length(lb)))) / (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            delta2 = (ub(min(j,length(ub))) - y(j)) / (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            
            r = rand();
            mut_pow = 1/(eta_m + 1);
            
            if r <= 0.5
                xy = 1 - delta1;
                val = 2*r + (1-2*r)*(xy^(eta_m+1));
                delta_q = val^mut_pow - 1;
            else
                xy = 1 - delta2;
                val = 2*(1-r) + 2*(r-0.5)*(xy^(eta_m+1));
                delta_q = 1 - val^mut_pow;
            end
            
            y(j) = y(j) + delta_q * (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            y(j) = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), y(j)));
        end
    end
end

function pop = EliteSelection(pop, nPop)
% 精英选择

    % 获取排名
    ranks = [pop.Rank];
    
    % 获取拥挤度距离
    crowding_distances = [pop.CrowdingDistance];
    
    % 创建排序指标：首先按排名，然后按拥挤度距离（降序）
    [~, indices] = sortrows([ranks', -crowding_distances']);
    
    % 选择前nPop个个体
    pop = pop(indices(1:nPop));
end

function result = Dominates(x, y)
% 判断x是否支配y

    % 检查输入是否为空
    if isempty(x) || isempty(y)
        result = false;
        return;
    end
    
    % 检查维度是否匹配
    if length(x) ~= length(y)
        result = false;
        return;
    end
    
    % 检查是否有无效值
    if any(isnan(x)) || any(isnan(y)) || any(isinf(x)) || any(isinf(y))
        result = false;
        return;
    end
    
    % 标准支配关系检查
    result = all(x <= y) && any(x < y);
end



