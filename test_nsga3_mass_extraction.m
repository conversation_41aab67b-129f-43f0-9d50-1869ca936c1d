% 测试NSGA3的质量提取机制修改
% 验证NSGA3是否能正确使用与NSGA2相同的质量提取逻辑

clear; clc;

% 添加必要的路径
addpath('Algorithms/NSGA-III');
addpath('Algorithms/NSGA-II');
addpath('Model');
addpath('Config');

fprintf('=== 测试NSGA3质量提取机制修改 ===\n\n');

try
    % 1. 检查NSGA3文件是否存在
    if exist('Algorithms/NSGA-III/RunNSGAIII.m', 'file') ~= 2
        error('NSGA3文件不存在');
    end
    fprintf('✓ NSGA3文件存在\n');
    
    % 2. 检查calculatePureGearMass函数是否存在
    nsga3_content = fileread('Algorithms/NSGA-III/RunNSGAIII.m');
    if contains(nsga3_content, 'function pure_mass = calculatePureGearMass')
        fprintf('✓ calculatePureGearMass函数已添加到NSGA3\n');
    else
        error('calculatePureGearMass函数未找到');
    end
    
    % 3. 检查applyCenterDistanceConstraints函数是否存在
    if contains(nsga3_content, 'function x_corrected = applyCenterDistanceConstraints')
        fprintf('✓ applyCenterDistanceConstraints函数已添加到NSGA3\n');
    else
        error('applyCenterDistanceConstraints函数未找到');
    end
    
    % 4. 检查传动比计算是否使用正确的变量索引
    if contains(nsga3_content, 'first_stage_idx = round(x_current(1))')
        fprintf('✓ 传动比计算使用正确的19变量编码\n');
    else
        error('传动比计算未使用正确的变量索引');
    end
    
    % 5. 检查质量提取是否只考虑第一前沿
    if contains(nsga3_content, 'for idx = F{1}  % 只考虑第一前沿（非支配解集）')
        fprintf('✓ 质量提取只考虑第一前沿（非支配解集）\n');
    else
        error('质量提取未限制在第一前沿');
    end
    
    % 6. 检查是否包含工程约束检查
    if contains(nsga3_content, 'contact_sf >= min_contact_safety && bending_sf >= min_bending_safety && ratio_error <= max_ratio_error')
        fprintf('✓ 包含工程约束检查（安全系数和传动比误差）\n');
    else
        error('缺少工程约束检查');
    end
    
    % 7. 检查收敛历史记录结构
    if contains(nsga3_content, 'convergence_history.algorithm_name = ''NSGA-III''')
        fprintf('✓ 收敛历史记录包含算法名称\n');
    else
        error('收敛历史记录缺少算法名称');
    end
    
    % 8. 比较NSGA2和NSGA3的质量提取逻辑
    nsga2_content = fileread('Algorithms/NSGA-II/RunNSGAII.m');
    
    % 检查关键逻辑是否一致
    nsga2_has_constraint_check = contains(nsga2_content, 'contact_sf >= min_contact_safety && bending_sf >= min_bending_safety && ratio_error <= max_ratio_error');
    nsga3_has_constraint_check = contains(nsga3_content, 'contact_sf >= min_contact_safety && bending_sf >= min_bending_safety && ratio_error <= max_ratio_error');
    
    if nsga2_has_constraint_check && nsga3_has_constraint_check
        fprintf('✓ NSGA2和NSGA3的约束检查逻辑一致\n');
    else
        error('NSGA2和NSGA3的约束检查逻辑不一致');
    end
    
    fprintf('\n=== 所有检查通过！NSGA3质量提取机制修改成功 ===\n');
    fprintf('\n修改总结：\n');
    fprintf('1. 添加了calculatePureGearMass函数，使用与NSGA2相同的19变量编码\n');
    fprintf('2. 添加了applyCenterDistanceConstraints函数\n');
    fprintf('3. 修正了传动比计算，使用正确的变量索引\n');
    fprintf('4. 质量提取只考虑第一前沿（非支配解集）\n');
    fprintf('5. 包含完整的工程约束检查（安全系数和传动比误差）\n');
    fprintf('6. 优先选择满足约束的解中的最小质量\n');
    fprintf('7. 如果没有满足约束的解，使用所有解中的最小质量\n');
    
catch ME
    fprintf('❌ 测试失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
end
