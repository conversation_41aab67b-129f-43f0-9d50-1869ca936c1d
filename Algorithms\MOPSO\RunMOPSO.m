function [population, objectives, convergence_history] = RunMOPSO(problem, params)
% RunMOPSO - 运行多目标粒子群优化算法
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON>, MOPSO: A proposal for multiple
% objective particle swarm optimization, Proceedings of the IEEE Congress
% on Evolutionary Computation, 2002, 1051-1056.

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

% MOPSO特定参数
w = 0.4;                     % 惯性权重
wdamp = 0.99;                % 惯性权重衰减率
c1 = 1;                      % 个体学习因子
c2 = 2;                      % 社会学习因子
nGrid = 10;                  % 网格划分数
alpha = 0.1;                 % 网格膨胀率

% 粒子速度限制
vMax = 0.2*(varMax-varMin);
vMin = -vMax;

fprintf('开始运行MOPSO算法...\n');

%% 初始化种群
empty_particle.Position = [];
empty_particle.Velocity = [];
empty_particle.Cost = [];

% 初始化粒子群
particle = repmat(empty_particle, nPop, 1);

% 初始化粒子的位置和速度
for i = 1:nPop
    % 随机初始化位置
    particle(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                particle(i).Position(idx) = round(particle(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(particle(i).Position(idx) - values));
                particle(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    % 初始化速度
    particle(i).Velocity = zeros(varSize);
    
    % 评估目标函数
    particle(i).Cost = problem.costFunction(particle(i).Position);
    problem.FE = problem.FE + 1;
end

% 初始化个体最优位置
pbest = particle;

% 创建并初始化存档
archive = UpdateArchive(particle, nPop, nGrid);

% 如果存档为空，使用所有粒子作为存档
if isempty(archive)
    archive = particle;
end

% 初始化收敛历史记录
convergence_history = struct();
convergence_history.iteration = zeros(maxIt + 1, 1);
convergence_history.min_mass = zeros(maxIt + 1, 1);
convergence_history.algorithm_name = 'MOPSO';

% 记录初始代的最小总质量
current_costs = vertcat(particle.Cost);
valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
if ~isempty(valid_costs)
    convergence_history.min_mass(1) = min(valid_costs(:, 1));  % 第一个目标是总质量
else
    convergence_history.min_mass(1) = inf;
end
convergence_history.iteration(1) = 0;

%% 主循环
for it = 1:maxIt
    % 检查存档是否为空
    if isempty(archive)
        % 如果存档为空，使用当前粒子作为存档
        archive = particle;
        disp('存档已重置为当前粒子群。');
    end
    
    % 从存档中选择全局最优位置
    rep = REPSelection([archive.Cost], nPop, nGrid);
    
    % 更新粒子
    for i = 1:nPop
        % 检查rep索引是否有效
        if isempty(rep) || i > length(rep) || rep(i) <= 0 || rep(i) > length(archive)
            % 如果rep索引无效，随机选择一个存档中的解
            leader_idx = randi(length(archive));
            leader = archive(leader_idx);
        else
            % 选择全局最优
            leader = archive(rep(i));
        end
        
        % 更新速度
        particle(i).Velocity = w*particle(i).Velocity ...
            + c1*rand(varSize).*(pbest(i).Position - particle(i).Position) ...
            + c2*rand(varSize).*(leader.Position - particle(i).Position);
        
        % 应用速度限制
        particle(i).Velocity = max(particle(i).Velocity, vMin);
        particle(i).Velocity = min(particle(i).Velocity, vMax);
        
        % 更新位置
        particle(i).Position = particle(i).Position + particle(i).Velocity;
        
        % 边界处理
        particle(i).Position = max(particle(i).Position, varMin);
        particle(i).Position = min(particle(i).Position, varMax);
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    particle(i).Position(idx) = round(particle(i).Position(idx));
                else
                    % 找到最接近的离散值
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(particle(i).Position(idx) - values));
                    particle(i).Position(idx) = values(closest_idx);
                end
            end
        end
        
        % 评估目标函数
        particle(i).Cost = problem.costFunction(particle(i).Position);
        problem.FE = problem.FE + 1;
    end
    
    % 更新个体最优位置
    pbest = UpdatePbest(pbest, particle);
    
    % 更新存档 - 合并当前存档和新粒子
    combined_archive = [archive; particle];
    new_archive = UpdateArchive(combined_archive, nPop, nGrid);

    % 如果新存档为空，保留一些最好的解
    if isempty(new_archive)
        % 从合并的存档中选择最好的解
        if ~isempty(combined_archive)
            costs = reshape([combined_archive.Cost], [], length(combined_archive))';
            [~, idx] = sort(costs(:, 1));
            archive = combined_archive(idx(1:min(nPop, length(idx))));
        else
            % 如果完全没有解，保留当前粒子
            archive = particle(1:min(nPop, length(particle)));
        end
    else
        archive = new_archive;
    end

    % 确保存档不为空
    if isempty(archive) && ~isempty(particle)
        % 如果存档仍为空，至少保留一些粒子
        costs = reshape([particle.Cost], [], length(particle))';
        [~, idx] = sort(costs(:, 1));
        archive = particle(idx(1:min(5, length(idx))));
    end
    
    % 更新惯性权重
    w = w * wdamp;

    % 记录当前迭代的最小总质量
    current_costs = vertcat(particle.Cost);
    valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
    if ~isempty(valid_costs)
        convergence_history.min_mass(it + 1) = min(valid_costs(:, 1));  % 第一个目标是总质量
    else
        convergence_history.min_mass(it + 1) = inf;
    end
    convergence_history.iteration(it + 1) = it;

    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(numel(archive)) ', 评价次数 = ' num2str(problem.FE) ', 最小质量 = ' num2str(convergence_history.min_mass(it + 1), '%.2f') ' kg']);
    end
end

% 返回最终结果
n_pareto = numel(archive);
population = zeros(n_pareto, nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = archive(i).Position;
    objectives(i, :) = archive(i).Cost;
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数

% 裁剪收敛历史记录到实际使用的长度
convergence_history.iteration = convergence_history.iteration(1:maxIt+1);
convergence_history.min_mass = convergence_history.min_mass(1:maxIt+1);

% 不再输出算法完成信息，由主程序统一处理
end

%% 辅助函数
function Archive = UpdateArchive(Archive, N, div)
% 更新存档 - 改进版本

    % 提取目标函数值
    if isempty(Archive)
        return;
    end

    % 找到非支配解
    costs = reshape([Archive.Cost], [], length(Archive))';

    % 使用改进的非支配排序
    try
        frontNo = NDSort(costs, length(Archive));
        nonDominated = Archive(frontNo == 1);
    catch
        % 如果排序失败，使用简单的方法
        nonDominated = findNonDominatedSolutions(Archive);
    end

    % 如果没有非支配解，保留一些最好的解
    if isempty(nonDominated)
        % 按第一个目标函数排序（假设是最小化质量）
        [~, idx] = sort(costs(:, 1));
        Archive = Archive(idx(1:min(N, length(idx))));
        return;
    end

    Archive = nonDominated;

    % 如果非支配解数量超过限制，基于网格的保留
    if length(Archive) > N
        try
            Del = Delete(reshape([Archive.Cost], [], length(Archive))', length(Archive)-N, div);
            Archive(Del) = [];
        catch
            % 如果删除失败，简单地保留前N个
            Archive = Archive(1:N);
        end
    end
end

function nonDominated = findNonDominatedSolutions(solutions)
% 简单的非支配解查找方法
    if isempty(solutions)
        nonDominated = [];
        return;
    end

    costs = reshape([solutions.Cost], [], length(solutions))';
    n = size(costs, 1);
    dominated = false(n, 1);

    for i = 1:n
        for j = 1:n
            if i ~= j && Dominates(costs(j, :), costs(i, :))
                dominated(i) = true;
                break;
            end
        end
    end

    nonDominated = solutions(~dominated);
end

function Del = Delete(PopObj, K, div)   
    N = size(PopObj, 1);

    % 计算每个解的网格位置
    fmax = max(PopObj, [], 1);
    fmin = min(PopObj, [], 1);
    d = (fmax-fmin)/div;
    GLoc = floor((PopObj-repmat(fmin, N, 1))./repmat(d, N, 1));
    GLoc(GLoc>=div) = div - 1;
    GLoc(isnan(GLoc)) = 0;

    % 计算每个网格的拥挤度
    [~, ~, Site] = unique(GLoc, 'rows');
    CrowdG = histcounts(Site, 1:max(Site)+1);

    % 删除K个解
    Del = false(1, N);
    while sum(Del) < K
        % 选择最拥挤的网格
        maxGrid = find(CrowdG==max(CrowdG));
        Temp = randi(length(maxGrid));
        Grid = maxGrid(Temp);
        % 从网格中随机删除一个解
        InGrid = find(Site==Grid);
        Temp = randi(length(InGrid));
        p = InGrid(Temp);
        Del(p) = true;
        Site(p) = NaN;
        CrowdG(Grid) = CrowdG(Grid) - 1;
    end
end

function REP = REPSelection(PopObj, N, div)
% 为每个粒子选择存档中的一个粒子作为全局最优位置

    NoP = size(PopObj, 1);
    
    % 如果PopObj为空，返回随机索引
    if isempty(PopObj) || NoP == 0
        REP = randi(max(1, NoP), 1, N);
        return;
    end
    
    % 计算每个解的网格位置
    fmax = max(PopObj, [], 1);
    fmin = min(PopObj, [], 1);
    
    % 检查fmax和fmin是否相等（导致除零错误）
    if any(fmax == fmin)
        % 如果某维度上所有值相等，给该维度一个微小的扰动
        equal_dims = (fmax == fmin);
        fmax(equal_dims) = fmax(equal_dims) + 1e-6;
        fmin(equal_dims) = fmin(equal_dims) - 1e-6;
    end
    
    d = (fmax-fmin)/div;
    fmin = repmat(fmin, NoP, 1);
    d = repmat(d, NoP, 1);
    GLoc = floor((PopObj-fmin)./d);
    GLoc(GLoc>=div) = div - 1;
    GLoc(isnan(GLoc)) = 0;
    
    % 检测每个解所属的网格
    [~, ~, Site] = unique(GLoc, 'rows');
    
    % 检查Site是否为空
    if isempty(Site)
        REP = randi(max(1, NoP), 1, N);
        return;
    end
    
    % 计算每个网格的拥挤度
    max_site = max(Site);
    if isempty(max_site) || max_site <= 0 || isnan(max_site)
        % 如果max_site无效，使用默认值
        CrowdG = ones(1, NoP);
    else
        % 使用histcounts计算拥挤度
        try
            CrowdG = histcounts(Site, 1:max_site+1);
        catch
            % 如果histcounts失败，使用备用方法
            CrowdG = zeros(1, max_site);
            for i = 1:length(Site)
                if Site(i) > 0 && Site(i) <= max_site
                    CrowdG(Site(i)) = CrowdG(Site(i)) + 1;
                end
            end
            % 确保没有零值（避免除零错误）
            CrowdG(CrowdG == 0) = 1;
        end
    end
    
    % 确保CrowdG不为空且没有零值
    if isempty(CrowdG) || any(CrowdG == 0)
        CrowdG = ones(size(CrowdG));
        if isempty(CrowdG)
            CrowdG = ones(1, max(1, NoP));
        end
    end
    
    % 轮盘赌选择
    try
        TheGrid = RouletteWheelSelection(N, 1./CrowdG);
    catch
        % 如果轮盘赌选择失败，随机选择
        TheGrid = randi(max(1, length(CrowdG)), 1, N);
    end
    
    REP = zeros(1, N);
    for i = 1:length(REP)
        InGrid = find(Site==TheGrid(i));
        if isempty(InGrid)
            % 如果没有找到对应网格的解，随机选择
            REP(i) = randi(max(1, NoP));
        else
            Temp = randi(length(InGrid));
            REP(i) = InGrid(Temp);
        end
    end
end

function Pbest = UpdatePbest(Pbest, Population)
% 更新每个粒子的个体最优位置

    for i = 1:length(Pbest)
        % 检查支配关系
        if Dominates(Population(i).Cost, Pbest(i).Cost)
            % 如果当前位置支配个体最优，则更新
            Pbest(i) = Population(i);
        elseif ~Dominates(Pbest(i).Cost, Population(i).Cost)
            % 如果互不支配，则随机选择
            if rand < 0.5
                Pbest(i) = Population(i);
            end
        end
    end
end

function result = Dominates(obj1, obj2)
% 判断obj1是否支配obj2
    result = all(obj1 <= obj2) && any(obj1 < obj2);
end

function [FrontNo, MaxFNo] = NDSort(PopObj, nSort)
% 快速非支配排序算法 - 修复版本

    [N, M] = size(PopObj);
    FrontNo = inf(1, N);
    MaxFNo = 0;

    if N == 0
        return;
    end

    % 计算支配关系
    nP = zeros(1, N);  % 支配当前解的解的数量
    sP = cell(1, N);   % 当前解支配的解的集合

    % 计算支配关系
    for i = 1:N
        sP{i} = [];
        nP(i) = 0;
        for j = 1:N
            if i ~= j
                % 检查解i是否支配解j
                if Dominates(PopObj(i,:), PopObj(j,:))
                    sP{i} = [sP{i}, j];  % i支配j
                elseif Dominates(PopObj(j,:), PopObj(i,:))
                    nP(i) = nP(i) + 1;   % j支配i
                end
            end
        end

        % 如果没有解支配当前解，则它属于第一层前沿
        if nP(i) == 0
            MaxFNo = 1;
            FrontNo(i) = 1;
        end
    end

    % 构建后续前沿层
    while MaxFNo < N && any(FrontNo == inf)
        for i = 1:N
            if FrontNo(i) == MaxFNo
                % 对于当前前沿层的每个解，更新它支配的解
                for j = sP{i}
                    nP(j) = nP(j) - 1;
                    if nP(j) == 0
                        FrontNo(j) = MaxFNo + 1;
                    end
                end
            end
        end
        MaxFNo = MaxFNo + 1;

        % 防止无限循环
        if MaxFNo > N
            break;
        end
    end

    % 确保所有解都被分配到某个前沿
    FrontNo(FrontNo == inf) = MaxFNo;
end

function index = RouletteWheelSelection(K, fitness)
% 轮盘赌选择

    % 处理负值或零值
    fitness = max(0, fitness);
    
    % 如果所有适应度为0，则等概率选择
    if sum(fitness) == 0
        fitness = ones(size(fitness));
    end
    
    % 计算累积概率
    cumProb = cumsum(fitness) / sum(fitness);
    
    % 轮盘赌选择
    index = zeros(1, K);
    for i = 1:K
        r = rand();
        index(i) = find(cumProb >= r, 1);
    end
end 