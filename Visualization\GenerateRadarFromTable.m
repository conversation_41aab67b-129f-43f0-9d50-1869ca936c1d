function GenerateRadarFromTable()
% GENERATERADARFROMTABLE 从性能指标表格生成雷达图
% 直接读取算法性能指标综合表.xlsx文件

% 设置默认字体为宋体
set(0, 'DefaultAxesFontName', 'SimSun');
set(0, 'DefaultTextFontName', 'SimSun');
set(0, 'DefaultUicontrolFontName', 'SimSun');

try
    % 读取Excel文件
    table_file = 'Results/算法性能指标综合表.xlsx';
    if ~exist(table_file, 'file')
        error('找不到文件: %s', table_file);
    end
    
    % 读取数据
    data_table = readtable(table_file);
    fprintf('✓ 成功读取性能指标表格\n');
    
    % 提取算法名称和指标数据
    alg_names = data_table.x__;

    % 提取指标数据（按顺序：GD, IGD, Spread, HV, Coverage, Time）
    data = [
        data_table.GD, ...
        data_table.IGD, ...
        data_table.Spread, ...
        data_table.HV, ...
        data_table.x____C_metric_, ...
        data_table.x_______
    ];
    
    % 指标名称
    indicator_names = {'GD', 'IGD', 'Spread', 'HV', 'Coverage', 'Time'};
    
    % 生成雷达图
    PlotRadarChart(data, alg_names, indicator_names);
    
catch e
    fprintf('生成雷达图时出错: %s\n', e.message);
    fprintf('错误位置: %s\n', e.stack(1).name);
end
end

function PlotRadarChart(data, alg_names, indicator_names)
% 绘制雷达图的核心函数

n_algs = length(alg_names);
n_indicators = length(indicator_names);

% 标准化数据到0.1-1.0范围，考虑指标方向性
norm_data = zeros(size(data));

% 定义指标方向：true表示越大越好，false表示越小越好
% 顺序：GD, IGD, Spread, HV, Coverage, Time
indicator_directions = [false, false, false, true, true, false];

for j = 1:n_indicators
    indicator_data = data(:, j);
    
    % 跳过全零的指标
    if all(indicator_data == 0)
        norm_data(:, j) = 0.1; % 全零设为最差性能
        continue;
    end
    
    % 过滤有效数据
    valid_data = indicator_data(indicator_data > 0);
    if isempty(valid_data)
        norm_data(:, j) = 0.1;
        continue;
    end
    
    min_val = min(valid_data);
    max_val = max(valid_data);
    
    if max_val > min_val
        if indicator_directions(j)
            % 越大越好的指标(HV, Coverage)：大值映射到外圈(1.0)
            norm_data(:, j) = 0.1 + 0.9 * (indicator_data - min_val) / (max_val - min_val);
        else
            % 越小越好的指标(GD, IGD, Spread, Time)：小值映射到外圈(1.0)
            norm_data(:, j) = 1.0 - 0.9 * (indicator_data - min_val) / (max_val - min_val) + 0.1;
        end
        
        % 处理0值（通常表示算法失败或无效）
        zero_indices = indicator_data == 0;
        if any(zero_indices)
            norm_data(zero_indices, j) = 0.1; % 0值设为最差性能
        end
    else
        % 如果所有值相同，设为中间值
        norm_data(:, j) = 0.5;
    end
end

% 创建图窗
screen_size = get(0, 'ScreenSize');
fig_width = min(1200, screen_size(3) * 0.8);
fig_height = min(1000, screen_size(4) * 0.8);
fig_x = (screen_size(3) - fig_width) / 2;
fig_y = (screen_size(4) - fig_height) / 2;
fig = figure('Position', [fig_x, fig_y, fig_width, fig_height], ...
    'Name', '多目标优化算法性能雷达图对比 (共9个算法)', 'NumberTitle', 'off', ...
    'Color', 'white', 'MenuBar', 'figure', 'ToolBar', 'figure', ...
    'Resize', 'on');

% 设置颜色方案
colors = [
    1.0000, 0.0000, 0.0000;  % 红色 - NSGA-II
    0.0000, 0.0000, 1.0000;  % 蓝色 - NSGA-III
    0.0000, 0.7000, 0.0000;  % 鲜绿色 - SPEA2
    0.8000, 0.0000, 0.8000;  % 亮紫色 - MOEA-D
    0.0000, 0.8000, 0.8000;  % 青绿色 - MOEA-D-DE
    1.0000, 0.6000, 0.0000;  % 橙色 - MOEA-D-M2M
    0.5000, 0.0000, 1.0000;  % 紫色 - MOPSO
    1.0000, 0.9500, 0.0000;  % 亮黄色 - MOGWO
    1.0000, 0.0000, 0.5000;  % 品红色 - MOWOA
];

% 确保颜色数量足够
if n_algs > size(colors, 1)
    colors = [colors; jet(n_algs - size(colors, 1))];
end

% 计算角度
angles = linspace(0, 2*pi, n_indicators + 1);

% 绘制3×3子图
for i = 1:n_algs
    subplot(3, 3, i);
    
    % 当前算法的数据
    current_data = [norm_data(i, :), norm_data(i, 1)]; % 闭合多边形
    
    % 绘制网格
    hold on;
    for r = 0.2:0.2:1.0
        plot(r * cos(angles), r * sin(angles), 'k:', 'LineWidth', 0.5, 'Color', [0.7 0.7 0.7]);
    end
    
    % 绘制轴线
    for j = 1:n_indicators
        plot([0, cos(angles(j))], [0, sin(angles(j))], 'k:', 'LineWidth', 0.5, 'Color', [0.7 0.7 0.7]);
    end
    
    % 绘制数据多边形
    plot(current_data .* cos(angles), current_data .* sin(angles), ...
        'Color', colors(i, :), 'LineWidth', 2.5, 'Marker', 'o', 'MarkerSize', 6, ...
        'MarkerFaceColor', colors(i, :), 'MarkerEdgeColor', 'white');
    
    % 填充多边形
    fill(current_data .* cos(angles), current_data .* sin(angles), ...
        colors(i, :), 'FaceAlpha', 0.2, 'EdgeColor', 'none');
    
    % 添加指标标签
    for j = 1:n_indicators
        label_radius = 1.15;
        text(label_radius * cos(angles(j)), label_radius * sin(angles(j)), ...
            indicator_names{j}, 'HorizontalAlignment', 'center', ...
            'VerticalAlignment', 'middle', 'FontSize', 10, 'FontWeight', 'bold');
    end
    
    % 设置坐标轴
    axis equal;
    axis([-1.3, 1.3, -1.3, 1.3]);
    axis off;
    
    % 添加标题
    title(alg_names{i}, 'FontSize', 12, 'FontWeight', 'bold', 'Color', colors(i, :));
    
    hold off;
end

% 添加总标题
sgtitle('多目标优化算法性能雷达图对比 (共9个算法)', 'FontSize', 16, 'FontWeight', 'bold');

% 添加说明文字
annotation('textbox', [0.02, 0.02, 0.96, 0.05], ...
    'String', '说明：雷达图中，GD、IGD、Spread、Time越小越好(向内圈更好)；HV、Coverage越大越好(向外圈更好)', ...
    'FontSize', 10, 'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
    'EdgeColor', 'none', 'BackgroundColor', 'none');

% 保存图像
save_path = 'Results/算法性能雷达图.png';
print(fig, save_path, '-dpng', '-r300');
fprintf('✓ 雷达图已保存: %s\n', save_path);

end
