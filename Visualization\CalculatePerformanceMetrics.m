function metrics = CalculatePerformanceMetrics(pareto_fronts, algorithm_names, computation_times)
% CalculatePerformanceMetrics 计算多目标优化算法的性能指标
% 与OptimizationMain.m中使用的函数完全一致
%
% 输入:
%   pareto_fronts - 包含各算法Pareto前沿的cell数组
%   algorithm_names - 算法名称的cell数组
%   computation_times - 每个算法的实际计算时间(秒)，可选参数
%
% 输出:
%   metrics - 包含各种性能指标的结构体

% 添加Metrics路径
addpath('Metrics');

% 设置默认算法名称（如果未提供）
if nargin < 2 || isempty(algorithm_names)
    algorithm_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA-D', 'MOEA-D-DE', 'MOEA-D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};
end

% 算法数量
n_algs = length(pareto_fronts);

% 处理计算时间参数
if nargin < 3 || isempty(computation_times)
    % 如果没有提供时间数据，生成模拟时间（与EvaluateAlgorithms.m一致）
    fprintf('警告: 未提供计算时间数据，将使用模拟值\n');
    computation_times = rand(1, n_algs) * 10 + 5;  % 模拟时间：5-15秒
elseif length(computation_times) ~= n_algs
    % 如果时间数据长度不匹配，调整或填充
    fprintf('警告: 时间数据长度与算法数量不匹配，进行调整\n');
    if length(computation_times) < n_algs
        % 不足的用0填充
        computation_times = [computation_times, zeros(1, n_algs - length(computation_times))];
    else
        % 多余的截断
        computation_times = computation_times(1:n_algs);
    end
end

% 初始化指标结构体
metrics = struct();
metrics.GD = zeros(1, n_algs);       % Generation Distance
metrics.IGD = zeros(1, n_algs);      % Inverted Generation Distance
metrics.Spread = zeros(1, n_algs);   % Spread (Diversity)
metrics.HV = zeros(1, n_algs);       % Hypervolume
metrics.Time = zeros(1, n_algs);     % Computation Time
metrics.Coverage = zeros(1, n_algs); % Coverage (平均覆盖率)

% 找出所有算法结果中的非支配解，作为参考前沿
all_solutions = [];
for i = 1:n_algs
    if ~isempty(pareto_fronts{i})
        all_solutions = [all_solutions; pareto_fronts{i}];
    end
end

if isempty(all_solutions)
    fprintf('警告: 没有有效的解集用于计算指标\n');
    metrics.AlgorithmNames = algorithm_names(1:n_algs);
    return;
end

% 找出参考前沿（所有解的非支配集）
reference_front = findNonDominatedSolutions(all_solutions);

% 计算nadir点（用于超体积计算）
nadir_point = max(all_solutions) * 1.1;

fprintf('参考前沿包含 %d 个解\n', size(reference_front, 1));

% 为每个算法计算指标
for i = 1:n_algs
    if isempty(pareto_fronts{i})
        % 如果算法没有结果，设置默认值
        metrics.GD(i) = Inf;
        metrics.IGD(i) = Inf;
        metrics.Spread(i) = 0;
        metrics.HV(i) = 0;
        metrics.Time(i) = computation_times(i);
        continue;
    end
    
    current_front = pareto_fronts{i};
    
    % 计算GD (Generation Distance)
    metrics.GD(i) = GD(current_front, reference_front);
        
    % 计算IGD (Inverted Generation Distance)
    metrics.IGD(i) = IGD(current_front, reference_front);
    
    % 计算Spread (Diversity)
    metrics.Spread(i) = Spread(current_front);
        
    % 计算HV (Hypervolume)
    [metrics.HV(i), ~] = HV(current_front, reference_front);
        
    % 记录计算时间
    metrics.Time(i) = computation_times(i);
end

% 计算Coverage指标（平均覆盖率，与EvaluateAlgorithms.m一致）
for i = 1:n_algs
    if isempty(pareto_fronts{i})
        metrics.Coverage(i) = 0;
        continue;
    end
    
    coverage_values = [];
    for j = 1:n_algs
        if i ~= j && ~isempty(pareto_fronts{j})
            coverage_values(end+1) = Coverage(pareto_fronts{i}, pareto_fronts{j});
        end
    end
    if ~isempty(coverage_values)
        metrics.Coverage(i) = mean(coverage_values);
    else
        metrics.Coverage(i) = 0;
    end
end

fprintf('性能指标计算完成\n');

% 保存指标名称和算法名称
metrics.IndicatorNames = {'GD', 'IGD', 'Spread', 'HV', 'Time', 'Coverage'};
metrics.AlgorithmNames = algorithm_names(1:n_algs);
end

% ================ 以下是评价指标计算函数 ================

function non_dominated = findNonDominatedSolutions(solutions)
% 找出非支配解集
n = size(solutions, 1);
dominated = false(n, 1);

for i = 1:n
    for j = 1:n
        if i ~= j
            % 检查j是否支配i
            if all(solutions(j, :) <= solutions(i, :)) && any(solutions(j, :) < solutions(i, :))
                dominated(i) = true;
                break;
            end
        end
    end
end

non_dominated = solutions(~dominated, :);
end
