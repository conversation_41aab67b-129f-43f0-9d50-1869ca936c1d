function [population, objectives, convergence_history] = RunSPEA2(problem, params)
% RunSPEA2 - 运行SPEA2算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, SPEA2: Improving the strength
% Pareto evolutionary algorithm, Proceedings of the Conference on
% Evolutionary Methods for Design, Optimization and Control with
% Applications to Industrial Problems, 2001, 95-100.

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

fprintf('开始运行SPEA2算法...\n');

%% 初始化种群
empty_individual.Position = [];
empty_individual.Cost = [];

pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    % 生成随机解
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    % 评估目标函数
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 计算初始种群适应度
Fitness = CalFitness([pop.Cost]);

% 初始化收敛历史记录
convergence_history = struct();
convergence_history.iteration = zeros(maxIt + 1, 1);
convergence_history.min_mass = zeros(maxIt + 1, 1);
convergence_history.algorithm_name = 'SPEA2';

% 记录初始代的最小总质量
current_costs = vertcat(pop.Cost);
valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
if ~isempty(valid_costs)
    convergence_history.min_mass(1) = min(valid_costs(:, 1));  % 第一个目标是总质量
else
    convergence_history.min_mass(1) = inf;
end
convergence_history.iteration(1) = 0;

%% 优化主循环
for it = 1:maxIt
    % 锦标赛选择
    matingPool = TournamentSelection(2, nPop, Fitness);
    
    % 生成子代
    offspring = repmat(empty_individual, nPop, 1);
    for i = 1:nPop/2
        % 选择父代
        p1 = matingPool(i*2-1);
        p2 = matingPool(i*2);
        
        % 生成两个子代
        [offspring(i*2-1), offspring(i*2)] = GAOperator(problem, pop(p1), pop(p2), pCrossover, pMutation, varMin, varMax);
        
        % 评估子代
        problem.FE = problem.FE + 2;
    end
    
    % 环境选择
    [pop, Fitness] = EnvironmentalSelection([pop; offspring], nPop);

    % 记录当前迭代的最小总质量
    current_costs = vertcat(pop.Cost);
    valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
    if ~isempty(valid_costs)
        convergence_history.min_mass(it + 1) = min(valid_costs(:, 1));  % 第一个目标是总质量
    else
        convergence_history.min_mass(it + 1) = inf;
    end
    convergence_history.iteration(it + 1) = it;

    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        % 计算非支配解数量
        n_nondom = sum(Fitness < 1);
        disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE) ', 最小质量 = ' num2str(convergence_history.min_mass(it + 1), '%.2f') ' kg']);
    end
end

% 提取非支配解集
nonDominated = pop(Fitness < 1);
if isempty(nonDominated)
    [~, best] = min(Fitness);
    nonDominated = pop(best);
end

% 返回最终结果
n_pareto = numel(nonDominated);
population = zeros(n_pareto, nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = nonDominated(i).Position;
    objectives(i, :) = nonDominated(i).Cost;
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数

% 裁剪收敛历史记录到实际使用的长度
convergence_history.iteration = convergence_history.iteration(1:maxIt+1);
convergence_history.min_mass = convergence_history.min_mass(1:maxIt+1);

% 不再输出算法完成信息，由主程序统一处理
end

%% 辅助函数
function Fitness = CalFitness(PopObj)
% 计算每个解的适应度

    [N, ~] = size(PopObj);

    %% 检测每两个解之间的支配关系
    Dominate = false(N);
    for i = 1:N-1
        for j = i+1:N
            k = any(PopObj(i,:) < PopObj(j,:)) - any(PopObj(i,:) > PopObj(j,:));
            if k == 1
                Dominate(i,j) = true;
            elseif k == -1
                Dominate(j,i) = true;
            end
        end
    end
    
    %% 计算S(i) - 每个解支配的其他解的数量
    S = sum(Dominate, 2);
    
    %% 计算R(i) - 每个解被其他解支配的强度总和
    R = zeros(1, N);
    for i = 1:N
        R(i) = sum(S(Dominate(:,i)));
    end
    
    %% 计算D(i) - 密度估计值
    Distance = pdist2(PopObj, PopObj);
    Distance(logical(eye(length(Distance)))) = inf;
    Distance = sort(Distance, 2);
    D = 1./(Distance(:, floor(sqrt(N))) + 2);
    
    %% 计算最终适应度
    Fitness = R + D';
end

function [Population, Fitness] = EnvironmentalSelection(Population, N)
% SPEA2的环境选择

    %% 计算每个解的适应度
    PopObj = reshape([Population.Cost], [], length(Population))';
    Fitness = CalFitness(PopObj);

    %% 环境选择
    Next = Fitness < 1;  % 选择所有非支配解
    if sum(Next) < N
        % 如果非支配解数量不足，按适应度排序选择
        [~, Rank] = sort(Fitness);
        Next(Rank(1:N)) = true;
    elseif sum(Next) > N
        % 如果非支配解数量过多，使用截断操作
        Del = Truncation(PopObj(Next,:), sum(Next) - N);
        Temp = find(Next);
        Next(Temp(Del)) = false;
    end
    
    % 下一代种群
    Population = Population(Next);
    Fitness = Fitness(Next);
end

function Del = Truncation(PopObj, K)
% 通过截断选择部分解

    %% 截断
    Distance = pdist2(PopObj, PopObj);
    Distance(logical(eye(length(Distance)))) = inf;
    Del = false(1, size(PopObj, 1));
    while sum(Del) < K
        Remain = find(~Del);
        Temp = sort(Distance(Remain, Remain), 2);
        [~, Rank] = sortrows(Temp);
        Del(Remain(Rank(1))) = true;
    end
end

function matingPool = TournamentSelection(K, N, Fitness)
% 锦标赛选择

    % 生成随机索引
    matingPool = zeros(1, N);
    nFitness = length(Fitness);
    
    % 确保K不大于nFitness
    K = min(K, nFitness);
    
    for i = 1:N
        % 随机选择K个个体
        candidates = randperm(nFitness, K);
        
        % 选择最好的一个
        [~, best] = min(Fitness(candidates));
        matingPool(i) = candidates(best);
    end
end

function [offspring1, offspring2] = GAOperator(problem, parent1, parent2, pc, pm, lb, ub)
% 遗传算法操作 - SBX交叉和多项式变异

    offspring1 = struct('Position', [], 'Cost', []);
    offspring2 = struct('Position', [], 'Cost', []);
    
    % 提取父代位置
    x1 = parent1.Position;
    x2 = parent2.Position;
    
    % 模拟二进制交叉(SBX)
    if rand <= pc
        eta_c = 15;  % 交叉分布指数
        
        % 对每个变量执行SBX
        for j = 1:length(x1)
            if rand <= 0.5
                y1 = x1(j);
                y2 = x2(j);
                
                % 确保y1 <= y2
                if y1 > y2
                    temp = y1;
                    y1 = y2;
                    y2 = temp;
                end
                
                % 计算子代
                if y1 ~= y2
                    rand_val = rand();
                    beta = 1 + 2 * (y1 - lb(min(j, length(lb)))) / (y2 - y1);
                    alpha = 2 - beta^(-eta_c-1);
                    
                    if rand_val <= 1/alpha
                        beta_q = (rand_val * alpha)^(1/(eta_c+1));
                    else
                        beta_q = (1/(2-rand_val*alpha))^(1/(eta_c+1));
                    end
                    
                    c1 = 0.5 * ((y1 + y2) - beta_q * (y2 - y1));
                    
                    beta = 1 + 2 * (ub(min(j, length(ub))) - y2) / (y2 - y1);
                    alpha = 2 - beta^(-eta_c-1);
                    
                    if rand_val <= 1/alpha
                        beta_q = (rand_val * alpha)^(1/(eta_c+1));
                    else
                        beta_q = (1/(2-rand_val*alpha))^(1/(eta_c+1));
                    end
                    
                    c2 = 0.5 * ((y1 + y2) + beta_q * (y2 - y1));
                    
                    % 边界处理
                    c1 = max(lb(min(j, length(lb))), min(ub(min(j, length(ub))), c1));
                    c2 = max(lb(min(j, length(lb))), min(ub(min(j, length(ub))), c2));
                    
                    % 随机交换c1和c2
                    if rand > 0.5
                        temp = c1;
                        c1 = c2;
                        c2 = temp;
                    end
                    
                    offspring1.Position(j) = c1;
                    offspring2.Position(j) = c2;
                else
                    offspring1.Position(j) = y1;
                    offspring2.Position(j) = y2;
                end
            else
                offspring1.Position(j) = x1(j);
                offspring2.Position(j) = x2(j);
            end
        end
    else
        % 不交叉
        offspring1.Position = x1;
        offspring2.Position = x2;
    end
    
    % 多项式变异
    offspring1.Position = PolynomialMutation(offspring1.Position, pm, lb, ub);
    offspring2.Position = PolynomialMutation(offspring2.Position, pm, lb, ub);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                offspring1.Position(idx) = round(offspring1.Position(idx));
                offspring2.Position(idx) = round(offspring2.Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx1] = min(abs(offspring1.Position(idx) - values));
                [~, closest_idx2] = min(abs(offspring2.Position(idx) - values));
                offspring1.Position(idx) = values(closest_idx1);
                offspring2.Position(idx) = values(closest_idx2);
            end
        end
    end
    
    % 评估子代
    offspring1.Cost = problem.costFunction(offspring1.Position);
    offspring2.Cost = problem.costFunction(offspring2.Position);
end

function y = PolynomialMutation(x, pm, lb, ub)
% 多项式变异

    y = x;
    eta_m = 20;  % 变异分布指数
    
    for j = 1:length(x)
        if rand < pm
            y_lower = lb(min(j, length(lb)));
            y_upper = ub(min(j, length(ub)));
            
            delta1 = (y(j) - y_lower) / (y_upper - y_lower);
            delta2 = (y_upper - y(j)) / (y_upper - y_lower);
            
            rand_val = rand();
            mut_pow = 1 / (eta_m + 1);
            
            if rand_val <= 0.5
                xy = 1 - delta1;
                val = 2 * rand_val + (1 - 2 * rand_val) * (xy^(eta_m + 1));
                delta_q = val^mut_pow - 1;
            else
                xy = 1 - delta2;
                val = 2 * (1 - rand_val) + (2 * rand_val - 1) * (xy^(eta_m + 1));
                delta_q = 1 - val^mut_pow;
            end
            
            y(j) = y(j) + delta_q * (y_upper - y_lower);
            y(j) = max(y_lower, min(y_upper, y(j)));
        end
    end
end 