function [pop, F, convergence_history] = RunMOEAD_DE(problem, params)
% RunMOEAD_DE - 运行基于差分进化的分解多目标进化算法 (MOEA/D-DE)
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   pop - 最终种群
%   F   - 最终的非支配解集目标函数值
%   convergence_history - 收敛历史记录
%
% 参考文献:
% <PERSON><PERSON> and <PERSON><PERSON>, Multiobjective optimization problems with complicated
% Pareto sets, MOEA/D and NSGA-II, IEEE Transactions on Evolutionary
% Computation, 2009, 13(2): 284-302.

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;

% MOEA/D-DE特定参数
delta = 0.9;                      % 邻居选择概率
nr = 2;                           % 每个子代最多替换的解数量

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

fprintf('开始运行MOEA/D-DE算法...\n');

%% 生成权重向量
[W, nPop] = UniformPoint(nPop, nObj);

%% 检测每个解的邻居
T = ceil(nPop/10);  % 邻居大小

% 计算权重向量之间的距离矩阵
B = pdist2(W, W);
[~, B] = sort(B, 2);
B = B(:, 1:T);      % 存储每个个体的T个最近邻居

%% 初始化种群
empty_individual.Position = [];
empty_individual.Cost = [];

pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    % 生成随机解
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    % 评估目标函数
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 初始化理想点
Z = min(reshape([pop.Cost], nObj, []), [], 2)';

% 初始化收敛历史记录
convergence_history = struct();
convergence_history.iteration = zeros(maxIt + 1, 1);
convergence_history.min_mass = zeros(maxIt + 1, 1);
convergence_history.algorithm_name = 'MOEA/D-DE';

% 记录初始代的最小总质量
current_costs = vertcat(pop.Cost);
valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
if ~isempty(valid_costs)
    convergence_history.min_mass(1) = min(valid_costs(:, 1));  % 第一个目标是总质量
else
    convergence_history.min_mass(1) = inf;
end
convergence_history.iteration(1) = 0;

%% 优化主循环
for it = 1:maxIt
    % 对每个解进行操作
    for i = 1:nPop
        % 选择父代
        if rand < delta
            % 从邻居中选择
            P = B(i, randperm(size(B, 2)));
        else
            % 从整个种群中选择
            P = randperm(nPop);
        end
        
        % 产生一个后代
        offspring = DEOperator(problem, pop(i), pop(P(1)), pop(P(2)), varMin, varMax);
        problem.FE = problem.FE + 1;
        
        % 更新理想点
        Z = min(Z, offspring.Cost);
        
        % 使用切比雪夫方法更新解
        P_selected = P(1:min(length(P), nr*5));  % 选择前nr*5个索引
        
        % 计算原解和新解的切比雪夫距离
        g_old = max(abs(reshape([pop(P_selected).Cost], nObj, [])' - repmat(Z, length(P_selected), 1)) .* W(P_selected, :), [], 2);
        g_new = max(repmat(abs(offspring.Cost - Z), length(P_selected), 1) .* W(P_selected, :), [], 2);
        
        % 找到应该替换的解
        replace_idx = find(g_old >= g_new, nr);
        
        % 更新解
        if ~isempty(replace_idx)
            for j = 1:length(replace_idx)
                pop(P_selected(replace_idx(j))) = offspring;
            end
        end
    end

    % 记录当前迭代的最小总质量
    current_costs = vertcat(pop.Cost);
    valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
    if ~isempty(valid_costs)
        convergence_history.min_mass(it + 1) = min(valid_costs(:, 1));  % 第一个目标是总质量
    else
        convergence_history.min_mass(it + 1) = inf;
    end
    convergence_history.iteration(it + 1) = it;

    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        % 计算非支配解数量
        nonDominated = GetNonDominatedSet(pop);
        n_nondom = numel(nonDominated);
        disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE) ', 最小质量 = ' num2str(convergence_history.min_mass(it + 1), '%.2f') ' kg']);
    end
end

% 提取非支配解集
nonDominated = GetNonDominatedSet(pop);

% 修改代码，确保返回的是数值矩阵而非结构体数组
n_pareto = numel(nonDominated);
population = zeros(n_pareto, problem.nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = nonDominated(i).Position;
    objectives(i, :) = nonDominated(i).Cost;
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数

% 裁剪收敛历史记录到实际使用的长度
convergence_history.iteration = convergence_history.iteration(1:maxIt+1);
convergence_history.min_mass = convergence_history.min_mass(1:maxIt+1);

% 返回转换后的数值矩阵
pop = population;
F = objectives;

% 不再输出算法完成信息，由主程序统一处理
end

%% 辅助函数
function [W, N] = UniformPoint(N, M)
% 生成均匀分布的权重向量

if M == 2
    % 二目标直接均匀分布
    W = zeros(N, M);
    for i = 1:N
        W(i, 1) = (i-1)/(N-1);
        W(i, 2) = 1-W(i, 1);
    end
else
    % 多目标使用Das和Dennis方法
    H1 = 1;
    while nchoosek(H1+M-1, M-1) <= N
        H1 = H1 + 1;
    end
    W = nchoosek(1:H1+M-1, M-1) - repmat(0:M-2, nchoosek(H1+M-1, M-1), 1) - 1;
    W = [W, repmat(H1, size(W, 1), 1)] - [zeros(size(W, 1), 1), W];
    W = W./H1;
    
    if size(W, 1) > N
        W = W(1:N, :);
    else
        N = size(W, 1);
    end
end
end

function offspring = DEOperator(problem, target, donor1, donor2, lb, ub)
% 差分进化算子
offspring = target;

% 差分进化参数
F = 0.5;    % 缩放因子
CR = 1.0;   % 交叉率

% 差分变异
v = target.Position + F * (donor1.Position - donor2.Position);

% 交叉
r = randi(length(target.Position));
for j = 1:length(target.Position)
    if rand > CR && j ~= r
        v(j) = target.Position(j);
    end
end

% 对离散变量特殊处理
if isfield(problem, 'discreteVars')
    for j = 1:length(problem.discreteVars)
        idx = problem.discreteVars(j).idx;
        if problem.discreteVars(j).isInteger
            v(idx) = round(v(idx));
        else
            % 找到最接近的离散值
            values = problem.discreteVars(j).values;
            [~, closest_idx] = min(abs(v(idx) - values));
            v(idx) = values(closest_idx);
        end
    end
end

% 边界处理
v = max(v, lb);
v = min(v, ub);

% 创建后代
offspring.Position = v;
offspring.Cost = problem.costFunction(v);
end

function nonDominated = GetNonDominatedSet(pop)
% 获取非支配解集

objValues = reshape([pop.Cost], [], length(pop))';
N = size(objValues, 1);
dominated = false(1, N);

for i = 1:N-1
    for j = i+1:N
        if ~dominated(i) && ~dominated(j)
            if all(objValues(i,:) <= objValues(j,:)) && any(objValues(i,:) < objValues(j,:))
                dominated(j) = true;
            elseif all(objValues(j,:) <= objValues(i,:)) && any(objValues(j,:) < objValues(i,:))
                dominated(i) = true;
            end
        end
    end
end

nonDominated = pop(~dominated);
end 