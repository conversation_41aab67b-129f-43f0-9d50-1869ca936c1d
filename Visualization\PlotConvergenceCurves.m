function PlotConvergenceCurves(optimization_results, selected_algorithms)
% PlotConvergenceCurves - 绘制各个算法的最小总质量迭代曲线
%
% 输入:
%   optimization_results - 优化结果结构体
%   selected_algorithms  - 选择的算法列表
%
% 功能:
%   1. 绘制各个算法的最小总质量收敛曲线
%   2. 保存图像到Results文件夹
%   3. 显示算法性能对比

fprintf('\n=== 绘制算法收敛曲线 ===\n');

% 检查是否有收敛历史数据
has_convergence_data = false;
valid_algorithms = {};

for i = 1:length(selected_algorithms)
    algorithm_name = selected_algorithms{i};
    field_name = convertToValidFieldName(algorithm_name);
    
    if isfield(optimization_results, field_name)
        result = optimization_results.(field_name);
        if isfield(result, 'convergence_history') && ~isempty(result.convergence_history)
            has_convergence_data = true;
            valid_algorithms{end+1} = algorithm_name;
        elseif isfield(result, 'info') && isfield(result.info, 'run_info') && ...
               isfield(result.info.run_info, 'convergence_history') && ...
               ~isempty(result.info.run_info.convergence_history)
            has_convergence_data = true;
            valid_algorithms{end+1} = algorithm_name;
        end
    end
end

if ~has_convergence_data
    fprintf('警告：没有找到收敛历史数据，无法绘制收敛曲线\n');
    return;
end

fprintf('找到 %d 个算法的收敛历史数据\n', length(valid_algorithms));

% 创建图形 - 调整为与Pareto图一致的尺寸，设置白色背景
screen_size = get(0, 'ScreenSize');
fig_width = 600;  % 与Pareto图保持一致的宽度
fig_height = 500; % 与Pareto图保持一致的高度
fig_x = (screen_size(3) - fig_width) / 2;
fig_y = (screen_size(4) - fig_height) / 2;
figure('Position', [fig_x, fig_y, fig_width, fig_height], ...
       'Color', 'white', ...
       'Name', '最小总质量收敛曲线', ...
       'NumberTitle', 'off');

% 定义颜色和标记 - 与Pareto图保持一致
colors = [
    [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
    [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
    [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
    [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
    [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
    [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
    [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
    [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
    [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
];

% 定义标记样式 - 与Pareto图保持一致
markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};

% 绘制收敛曲线
hold on;
legend_entries = {};
min_mass_final = [];
algorithm_names_final = {};

for i = 1:length(valid_algorithms)
    algorithm_name = valid_algorithms{i};
    field_name = convertToValidFieldName(algorithm_name);
    result = optimization_results.(field_name);
    
    % 获取收敛历史
    convergence_history = [];
    if isfield(result, 'convergence_history') && ~isempty(result.convergence_history)
        convergence_history = result.convergence_history;
    elseif isfield(result, 'info') && isfield(result.info, 'run_info') && ...
           isfield(result.info.run_info, 'convergence_history') && ...
           ~isempty(result.info.run_info.convergence_history)
        convergence_history = result.info.run_info.convergence_history;
    end
    
    if ~isempty(convergence_history) && isfield(convergence_history, 'iteration') && ...
       isfield(convergence_history, 'min_mass')
        
        iterations = convergence_history.iteration;
        min_mass = convergence_history.min_mass;
        
        % 过滤掉无效值
        valid_idx = ~isinf(min_mass) & ~isnan(min_mass) & min_mass > 0;
        if sum(valid_idx) > 0
            iterations_valid = iterations(valid_idx);
            min_mass_valid = min_mass(valid_idx);
            
            % 绘制曲线 - 使用实线和Pareto图相同的标记
            color_idx = mod(i-1, size(colors, 1)) + 1;
            marker_idx = mod(i-1, length(markers)) + 1;

            plot(iterations_valid, min_mass_valid, ...
                'Color', colors(color_idx, :), ...
                'LineStyle', '-', ...  % 统一使用实线
                'Marker', markers{marker_idx}, ...
                'MarkerSize', 6, ...  % 调小标记尺寸
                'MarkerIndices', 1:max(1, floor(length(iterations_valid)/10)):length(iterations_valid), ...
                'LineWidth', 1.5, ...  % 线条粗细，也控制标记边缘
                'MarkerFaceColor', colors(color_idx, :), ...  % 填充标记
                'MarkerEdgeColor', colors(color_idx, :) * 0.7, ...  % 稍深的边缘颜色
                'DisplayName', algorithm_name);  % 确保图例显示线条+标记
            
            legend_entries{end+1} = algorithm_name;
            min_mass_final(end+1) = min_mass_valid(end);
            algorithm_names_final{end+1} = algorithm_name;
            
            fprintf('  %s: 初始质量 = %.2f kg, 最终质量 = %.2f kg, 改善 = %.2f%%\n', ...
                algorithm_name, min_mass_valid(1), min_mass_valid(end), ...
                (min_mass_valid(1) - min_mass_valid(end)) / min_mass_valid(1) * 100);
        else
            fprintf('  %s: 没有有效的收敛数据\n', algorithm_name);
        end
    else
        fprintf('  %s: 收敛历史数据格式不正确\n', algorithm_name);
    end
end

% 设置图形属性 - 与Pareto图保持一致的学术论文风格
ax = gca;
ax.FontName = 'SimSun';
ax.FontSize = 12;
ax.LineWidth = 1.5;         % 统一边框线条粗细
ax.GridLineStyle = ':';
ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
ax.TickLength = [0.01 0.01];
ax.TickDir = 'in';          % 内向刻度
ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
ax.YColor = [0.2 0.2 0.2];
ax.Color = 'white';         % 设置背景为白色
ax.Box = 'on';

xlabel('迭代次数', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');
ylabel('最小总质量 (kg)', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');

% 设置标题，使用与Pareto图相同的方法
h_title = title('最小总质量收敛曲线', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');
% 设置固定标题位置，使其位于图框外上方
set(h_title, 'Units', 'normalized', 'Position', [0.5, 1.02, 0], 'HorizontalAlignment', 'center');

grid on;

% 添加图例，放在右上角，设置为学术论文风格 - 完全按照Pareto图设置
lgd = legend('Location', 'northeast', 'FontName', 'SimSun', 'FontSize', 11);
lgd.Box = 'on';
lgd.LineWidth = 0.5;  % 更细的边框线条
% 设置图例中的标记大小为适中值，缩短线条长度
lgd.ItemTokenSize = [18, 18];  % [宽度, 高度] - 缩短线条长度，参考Pareto图
xlim([0, inf]);
ylim([0, inf]);

% 调整图形边距，与Pareto图保持一致
set(gca, 'Position', [0.13, 0.15, 0.7, 0.75]);

% 保存图像
if ~exist('Results', 'dir')
    mkdir('Results');
end

% 保存为多种格式
timestamp = datestr(now, 'yyyymmdd_HHMMSS');
fig_filename_png = sprintf('Results/convergence_curves_%s.png', timestamp);
fig_filename_fig = sprintf('Results/convergence_curves_%s.fig', timestamp);

try
    saveas(gcf, fig_filename_png);
    saveas(gcf, fig_filename_fig);
    fprintf('收敛曲线已保存到: %s\n', fig_filename_png);
    fprintf('MATLAB图形文件已保存到: %s\n', fig_filename_fig);
catch ME
    fprintf('保存图像失败: %s\n', ME.message);
end

% 显示算法性能排名
if ~isempty(min_mass_final)
    fprintf('\n=== 算法性能排名（按最终最小质量） ===\n');
    [sorted_mass, sort_idx] = sort(min_mass_final);
    for i = 1:length(sort_idx)
        fprintf('%d. %s: %.2f kg\n', i, algorithm_names_final{sort_idx(i)}, sorted_mass(i));
    end
end

fprintf('收敛曲线绘制完成！\n\n');
end

function field_name = convertToValidFieldName(algorithm_name)
% 将算法名称转换为有效的MATLAB字段名
field_name = algorithm_name;
field_name = strrep(field_name, '-', '_');  % 替换连字符
field_name = strrep(field_name, '/', '_');  % 替换斜杠
field_name = strrep(field_name, ' ', '_');  % 替换空格
% 确保以字母开头
if ~isempty(field_name) && ~isletter(field_name(1))
    field_name = ['alg_', field_name];
end
end
