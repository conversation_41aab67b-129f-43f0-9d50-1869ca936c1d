function PlotParetoFronts(all_results, algorithm_names, save_plots)
% PlotParetoFronts 绘制多目标优化算法的目标空间分布对比图
%
% 输入参数:
%   all_results - 包含所有算法结果的cell数组，每个元素是一个N×3的矩阵
%   algorithm_names - 算法名称的cell数组
%   save_plots - 是否保存图片（可选，默认为true）
%
% 输出:
%   无返回值，直接显示图形并可选保存

if nargin < 3
    save_plots = true;
end

% 验证输入参数
if length(all_results) ~= length(algorithm_names)
    error('算法结果数量与算法名称数量不匹配');
end

% 过滤空结果和异常数据
valid_indices = [];
for i = 1:length(all_results)
    if ~isempty(all_results{i}) && size(all_results{i}, 2) >= 3
        % 检查质量数据是否合理（第一列是质量）
        masses = all_results{i}(:, 1);
        if max(masses) <= 10000 && min(masses) > 0  % 质量应该在合理范围内
            valid_indices = [valid_indices, i];
            fprintf('✓ %s: %d个解, 质量范围: %.2f - %.2f kg\n', ...
                algorithm_names{i}, size(all_results{i}, 1), min(masses), max(masses));
        else
            fprintf('❌ %s: 质量数据异常 (%.2f - %.2f kg)，已过滤\n', ...
                algorithm_names{i}, min(masses), max(masses));
        end
    end
end

if isempty(valid_indices)
    fprintf('警告：没有有效的算法结果可以绘制\n');
    return;
end

% 只保留有效结果
all_results = all_results(valid_indices);
algorithm_names = algorithm_names(valid_indices);

fprintf('最终使用%d个算法的数据进行绘图\n', length(algorithm_names));

% 设置默认字体为宋体，确保所有文字统一
set(0, 'DefaultAxesFontName', 'SimSun');
set(0, 'DefaultTextFontName', 'SimSun');
set(0, 'DefaultUicontrolFontName', 'SimSun');
set(0, 'DefaultAxesFontSize', 12);
set(0, 'DefaultTextFontSize', 12);

% 定义更加鲜艳活泼的颜色方案
colors = [
    [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
    [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
    [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
    [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
    [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
    [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
    [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
    [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
    [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
];

% 定义边线颜色 - 增强立体感
edgeColors = [
    [0.7000, 0.0000, 0.0000];  % 深红色边线
    [0.0000, 0.0000, 0.7000];  % 深蓝色边线
    [0.0000, 0.5000, 0.0000];  % 深绿色边线
    [0.6000, 0.0000, 0.6000];  % 深紫色边线
    [0.0000, 0.6000, 0.6000];  % 深青绿色边线
    [0.7000, 0.4000, 0.0000];  % 深橙色边线
    [0.3500, 0.0000, 0.7000];  % 深紫色边线
    [0.7500, 0.7500, 0.0000];  % 深黄色边线
    [0.7000, 0.0000, 0.4000];  % 深品红色边线
];

% 定义标记样式 - 使用更清晰区分的形状和大小
markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
markerSizes = [60, 65, 55, 70, 60, 65, 55, 70, 60]; % 增大标记尺寸，提高可见性

% 获取屏幕尺寸用于窗口定位
screenSize = get(0, 'ScreenSize');
figWidth = 600;
figHeight = 500;

% 提取目标函数值
all_f1 = []; all_f2 = []; all_f3 = [];

for i = 1:length(all_results)
    if ~isempty(all_results{i})
        all_f1 = [all_f1; all_results{i}(:, 1)];
        all_f2 = [all_f2; abs(all_results{i}(:, 2))];  % 取绝对值确保为正数
        all_f3 = [all_f3; abs(all_results{i}(:, 3))];  % 取绝对值确保为正数
    end
end

% 计算居中位置
figX = (screenSize(3) - figWidth) / 2;
figY = (screenSize(4) - figHeight) / 2;

% 定义堆叠偏移量（每个窗口向右下角偏移）
stackOffset = 25; % 像素偏移量

% 创建第一个独立图窗：三维目标空间分布图（最底层）
fig1 = figure('Name', '三维目标空间分布图', 'NumberTitle', 'off', 'Color', 'white', 'Position', [figX, figY, figWidth, figHeight]);
Plot3DParetoFront(all_results, algorithm_names, all_f1, all_f2, all_f3, 'southeast'); % 图例位置设为右下角

% 设置标题，并调整位置使其更整齐
h_title = title('齿轮总质量-最小安全系数目标空间分布图', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');
set(h_title, 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
title_pos = get(h_title, 'Position');
set(h_title, 'Position', [title_pos(1), title_pos(2) + 0.02, title_pos(3)]);

% 创建第二个独立图窗：质量-接触安全系数（中间层，向右下偏移）
fig2 = figure('Name', '质量-接触安全系数', 'NumberTitle', 'off', 'Color', 'white', 'Position', [figX + stackOffset, figY - stackOffset, figWidth, figHeight]);
PlotMassVsContactSafety(all_results, algorithm_names, all_f1, all_f3);
h_title2 = title('质量-最小接触安全系数', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');
% 设置固定标题位置，使其位于图框外上方，稍微降低位置
set(h_title2, 'Units', 'normalized', 'Position', [0.5, 1.02, 0], 'HorizontalAlignment', 'center');

% 创建第三个独立图窗：质量-弯曲安全系数（最上层，向右下再偏移）
fig3 = figure('Name', '质量-弯曲安全系数', 'NumberTitle', 'off', 'Color', 'white', 'Position', [figX + 2*stackOffset, figY - 2*stackOffset, figWidth, figHeight]);
PlotMassVsBendingSafety(all_results, algorithm_names, all_f1, all_f2);
h_title3 = title('质量-最小弯曲安全系数', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');
% 设置固定标题位置，使其位于图框外上方，与接触安全系数图保持一致的间距
set(h_title3, 'Units', 'normalized', 'Position', [0.5, 1.02, 0], 'HorizontalAlignment', 'center');

% 确保所有图形完全渲染
drawnow;

% 等待一小段时间确保图形完全渲染
pause(0.5);



% 保存前沿图
if save_plots
    try
        % 确保Results目录存在
        results_dir = 'Results';
        if ~exist(results_dir, 'dir')
            mkdir(results_dir);
        end

        % 保存第一个图窗：三维目标空间分布图
        png_filename1 = fullfile(results_dir, '三维目标空间分布图.png');
        try
            print(fig1, png_filename1, '-dpng', '-r300');
            fprintf('已保存图表: %s\n', png_filename1);
        catch
            saveas(fig1, png_filename1, 'png');
            fprintf('已保存图表: %s (备选方案)\n', png_filename1);
        end

        % 保存第二个图窗：质量-接触安全系数
        png_filename2 = fullfile(results_dir, '质量-接触安全系数.png');
        try
            print(fig2, png_filename2, '-dpng', '-r300');
            fprintf('已保存图表: %s\n', png_filename2);
        catch
            saveas(fig2, png_filename2, 'png');
            fprintf('已保存图表: %s (备选方案)\n', png_filename2);
        end

        % 保存第三个图窗：质量-弯曲安全系数
        png_filename3 = fullfile(results_dir, '质量-弯曲安全系数.png');
        try
            print(fig3, png_filename3, '-dpng', '-r300');
            fprintf('已保存图表: %s\n', png_filename3);
        catch
            saveas(fig3, png_filename3, 'png');
            fprintf('已保存图表: %s (备选方案)\n', png_filename3);
        end

        % 保存为高质量的EPS格式
        eps_filename1 = fullfile(results_dir, '三维目标空间分布图.eps');
        eps_filename2 = fullfile(results_dir, '质量-接触安全系数.eps');
        eps_filename3 = fullfile(results_dir, '质量-弯曲安全系数.eps');

        try
            print(fig1, eps_filename1, '-depsc', '-r300');
            print(fig2, eps_filename2, '-depsc', '-r300');
            print(fig3, eps_filename3, '-depsc', '-r300');
            fprintf('已保存EPS格式: %s, %s, %s\n', eps_filename1, eps_filename2, eps_filename3);
        catch
            saveas(fig1, eps_filename1, 'epsc');
            saveas(fig2, eps_filename2, 'epsc');
            saveas(fig3, eps_filename3, 'epsc');
            fprintf('已保存EPS格式: %s, %s, %s (备选方案)\n', eps_filename1, eps_filename2, eps_filename3);
        end

    catch e
        fprintf('保存图片时出错: %s\n', e.message);
    end
end

fprintf('完成! 所有目标空间分布图表已分别显示在三个独立窗口中，并保存到Results文件夹\n');
end

function [varargout] = Plot3DParetoFront(all_results, alg_names, f1_all, f2_all, f3_all, legend_position)
    % 绘制三维目标空间分布图
    % 如果未指定图例位置，默认为东北角
    if nargin < 6
        legend_position = 'northeast';
    end

    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;

    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];

    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];

    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记

    % 获取当前figure
    fig = gcf;

    % 创建轴并保持
    hold on;
    grid on;
    box on;

    % 图例条目
    legendEntries = {};

    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end

        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f2 = abs(all_results{i}(:, 2));   % 弯曲安全系数（取绝对值确保为正）
        f3 = abs(all_results{i}(:, 3));   % 接触安全系数（取绝对值确保为正）

        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;

        % 使用散点图绘制Pareto前沿 - 修改坐标轴顺序，使z轴表示质量
        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f2_jitter = f2 + (rand(size(f2))-0.5)*jitter*mean(f2);
        f3_jitter = f3 + (rand(size(f3))-0.5)*jitter*mean(f3);

        scatter3(f2_jitter, f3_jitter, f1, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.9, ... % 减少透明度，提高可见性
               'DisplayName', alg_names{i});

        legendEntries{end+1} = alg_names{i};
    end

    % 设置坐标轴范围，确保数据分布均匀美观
    f1_range = max(f1_all) - min(f1_all);
    f2_range = max(f2_all) - min(f2_all);
    f3_range = max(f3_all) - min(f3_all);

    % 使用适当的边距，确保视觉平衡
    margin_percent = 0.08;  % 8%边距

    xlim([min(f2_all) - f2_range*margin_percent, max(f2_all) + f2_range*margin_percent]);
    ylim([min(f3_all) - f3_range*margin_percent, max(f3_all) + f3_range*margin_percent]);
    zlim([min(f1_all) - f1_range*margin_percent, max(f1_all) + f1_range*margin_percent]);

    % 设置合适的刻度间隔 - 安全系数以0.05为间隔，质量以0.5千克为间隔
    % 弯曲安全系数轴（X轴）：以0.05为间隔
    bend_safety_min = floor(min(f2_all)*20)/20;    % 向下取整到最近的0.05
    bend_safety_max = ceil(max(f2_all)*20)/20;     % 向上取整到最近的0.05
    xticks(bend_safety_min:0.05:bend_safety_max);

    % 接触安全系数轴（Y轴）：以0.05为间隔
    contact_safety_min = floor(min(f3_all)*20)/20;    % 向下取整到最近的0.05
    contact_safety_max = ceil(max(f3_all)*20)/20;     % 向上取整到最近的0.05
    yticks(contact_safety_min:0.05:contact_safety_max);

    % 质量轴（Z轴）：以0.5千克为间隔
    mass_min = floor(min(f1_all)/500)*500;  % 向下取整到最近的500
    mass_max = ceil(max(f1_all)/500)*500;   % 向上取整到最近的500
    zticks(mass_min:500:mass_max);

    % 设置固定视角，调整为更好地查看
    view([-30, 25]);

    % 设置坐标轴标签 - 使标题方向与3D坐标轴边框线的几何方向平行
    h_xlabel = xlabel('最小弯曲安全系数', 'FontName', chineseFont, 'FontSize', fontSize);
    h_ylabel = ylabel('最小接触安全系数', 'FontName', chineseFont, 'FontSize', fontSize);
    h_zlabel = zlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);

    % 计算3D坐标轴边框线在屏幕上的投影方向
    % 这是基于3D坐标轴的实际几何方向，而不是视角

    % 使用3D变换矩阵计算轴线在屏幕上的投影方向
    % 获取当前的视图变换矩阵
    [az, el] = view();

    % 计算3D到2D的投影变换
    % 基于当前视角计算轴向量在屏幕上的投影

    % 使用MATLAB的view变换约定来计算轴向量投影
    % MATLAB的3D视图变换：先绕Z轴旋转-az，再绕新的Y轴旋转el

    % 将角度转换为弧度
    az_rad = deg2rad(az);
    el_rad = deg2rad(el);

    % 方位角旋转矩阵（绕Z轴，注意MATLAB使用-az）
    Rz = [cos(-az_rad), -sin(-az_rad), 0;
          sin(-az_rad),  cos(-az_rad), 0;
          0,             0,            1];

    % 仰角旋转矩阵（绕Y轴）
    Ry = [cos(el_rad),  0, sin(el_rad);
          0,            1, 0;
          -sin(el_rad), 0, cos(el_rad)];

    % 组合变换矩阵（注意顺序：先方位角，后仰角）
    R = Ry * Rz;

    % 定义3D坐标轴的单位向量
    x_axis_3d = [1; 0; 0];  % X轴方向
    y_axis_3d = [0; 1; 0];  % Y轴方向

    % 应用变换得到在观察坐标系中的方向
    x_axis_view = R * x_axis_3d;
    y_axis_view = R * y_axis_3d;

    % 投影到屏幕平面（忽略Z分量，只取X和Y分量）
    x_axis_screen = [x_axis_view(1), x_axis_view(2)];
    y_axis_screen = [y_axis_view(1), y_axis_view(2)];

    % 计算屏幕上的角度
    x_axis_angle = atan2d(x_axis_screen(2), x_axis_screen(1));
    y_axis_angle = atan2d(y_axis_screen(2), y_axis_screen(1));

    % 调整角度，确保文字的下方（基线）朝向坐标轴的外侧
    % 对于X轴：如果角度在第二或第三象限，需要翻转180度
    if x_axis_angle > 90 || x_axis_angle < -90
        x_axis_angle = x_axis_angle + 180;
    end

    % 对于Y轴：如果角度在第二或第三象限，需要翻转180度
    if y_axis_angle > 90 || y_axis_angle < -90
        y_axis_angle = y_axis_angle + 180;
    end

    % 微调角度以获得更好的对齐效果
    x_axis_angle_adjustment = -20;  % X轴标签角度微调（度）- 弯曲安全系数
    y_axis_angle_adjustment = 25;   % Y轴标签角度微调（度）- 接触安全系数

    x_axis_angle = x_axis_angle + x_axis_angle_adjustment;
    y_axis_angle = y_axis_angle + y_axis_angle_adjustment;

    % 设置标题旋转角度，使其与对应的轴线方向平行且文字下方朝外
    set(h_xlabel, 'Rotation', x_axis_angle);
    set(h_ylabel, 'Rotation', y_axis_angle);
    set(h_zlabel, 'Rotation', 90);  % Z轴标签保持竖直

    % 获取坐标轴范围
    xlims = xlim;
    ylims = ylim;
    zlims = zlim;

    % 计算轴的中点位置
    x_mid = (xlims(1) + xlims(2)) / 2;
    y_mid = (ylims(1) + ylims(2)) / 2;
    z_mid = (zlims(1) + zlims(2)) / 2;

    % 调整标签位置，使其在轴线的中间位置但在图形外部
    % X轴标签（弯曲安全系数）：X轴中点，Y轴最小值外侧，Z轴最小值外侧 - 增加外移距离
    set(h_xlabel, 'Position', [x_mid, ylims(1) - 0.12*(ylims(2)-ylims(1)), zlims(1) - 0.05*(zlims(2)-zlims(1))]);

    % Y轴标签（接触安全系数）：X轴最小值外侧，Y轴中点，Z轴最小值外侧 - 适度外移
    set(h_ylabel, 'Position', [xlims(1) - 0.24*(xlims(2)-xlims(1)), y_mid, zlims(1) - 0.05*(zlims(2)-zlims(1))]);

    % Z轴标签（总质量）：让其尽量靠近Z轴
    z_label_pos = get(h_zlabel, 'Position');
    set(h_zlabel, 'Position', [z_label_pos(1) + 0.15*(xlims(2)-xlims(1)), z_label_pos(2) + 0.15*(ylims(2)-ylims(1)), z_label_pos(3)]);

    % 设置标签对齐方式
    set(h_xlabel, 'HorizontalAlignment', 'center', 'VerticalAlignment', 'top');
    set(h_ylabel, 'HorizontalAlignment', 'center', 'VerticalAlignment', 'top');
    set(h_zlabel, 'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle');

    % 使用线性坐标，显示更直观
    set(gca, 'ZScale', 'linear');

    % 自定义Z轴刻度标签，显示小数值
    zticks_vals = get(gca, 'ZTick');
    ztick_labels = cell(size(zticks_vals));
    for i = 1:length(zticks_vals)
        ztick_labels{i} = sprintf('%.1f', zticks_vals(i)/1000);
    end
    set(gca, 'ZTickLabel', ztick_labels);

    % 在Z轴顶端上方添加×10³标记（水平显示）
    zlim_vals = get(gca, 'ZLim');
    xlim_vals = get(gca, 'XLim');
    ylim_vals = get(gca, 'YLim');

    % 将×10³放在左侧z轴边框顶端附近
    text_x = min(xlim_vals);  % 左侧边框的x坐标
    text_y = max(ylim_vals);  % 左侧边框的y坐标（远离视角）
    text_z = max(zlim_vals) + (max(zlim_vals) - min(zlim_vals)) * 0.05;  % z轴顶端稍上方

    text(text_x, text_y, text_z, '×10³', ...
         'FontName', chineseFont, 'FontSize', fontSize, ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
         'Color', [0.2 0.2 0.2], 'Rotation', 0);  % 水平显示，不旋转

    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.5;         % 统一边框线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    ax.ZColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';

    % 确保坐标轴标签的对齐方式
    ax.XLabel.HorizontalAlignment = 'center';
    ax.YLabel.HorizontalAlignment = 'center';
    ax.ZLabel.HorizontalAlignment = 'center';
    ax.XLabel.VerticalAlignment = 'top';
    ax.YLabel.VerticalAlignment = 'bottom';
    ax.ZLabel.VerticalAlignment = 'bottom';

    % 添加图例，智能选择最佳位置避免遮挡数据
    lgd = legend(legendEntries, 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    lgd.Color = [1 1 1 0.9];  % 白色半透明背景，减少遮挡感
    lgd.EdgeColor = [0.2 0.2 0.2];  % 深灰色边框

    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 减小图例中的标记
    end

    % 直接设置图例到左上角的固定位置，避免遮挡数据和边框
    % 手动设置图例位置到左上角，但留出足够空间
    ax_pos = get(gca, 'Position');

    % 计算图例的精确位置
    legend_width = 0.12;   % 图例宽度
    legend_height = 0.25;  % 图例高度
    legend_x = ax_pos(1) + 0.08;  % 距离坐标轴左边界的距离
    legend_y = ax_pos(2) + ax_pos(4) - legend_height - 0.20;  % 距离顶部更远，避免压到边框

    % 设置图例位置
    lgd.Position = [legend_x, legend_y, legend_width, legend_height];

    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end

function [varargout] = PlotMassVsBendingSafety(all_results, alg_names, f1_all, f2_all)
    % 绘制质量-弯曲安全系数图

    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;

    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];

    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];

    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记

    % 获取当前figure
    fig = gcf;

    % 创建轴并保持
    hold on;
    grid on;
    box on;

    % 图例条目
    legendEntries = {};

    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end

        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f2 = abs(all_results{i}(:, 2));   % 弯曲安全系数（取绝对值确保为正）

        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;

        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f1_jitter = f1 + (rand(size(f1))-0.5)*jitter*mean(f1);
        f2_jitter = f2 + (rand(size(f2))-0.5)*jitter*mean(f2);

        % 使用散点图绘制Pareto前沿
        scatter(f1_jitter, f2_jitter, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.9, ... % 减少透明度，提高可见性
               'DisplayName', alg_names{i});

        legendEntries{end+1} = alg_names{i};
    end

    % 设置坐标轴范围，确保数据分布均匀美观
    f1_range = max(f1_all) - min(f1_all);
    f2_range = max(f2_all) - min(f2_all);

    % 使用适当的边距
    margin_percent = 0.08;  % 8%边距

    xlim([min(f1_all) - f1_range*margin_percent, 5500]);
    % 弯曲安全系数Y轴范围设置为显示到1.6
    ylim([min(f2_all) - f2_range*margin_percent, max(1.6, max(f2_all) + f2_range*margin_percent)]);

    % 设置合适的刻度间隔 - 质量以0.5千克为间隔，安全系数以0.1为间隔
    % 质量轴（X轴）：以0.5千克为间隔，固定最大值为5500，显示为5.5（×10³）
    mass_min = floor(min(f1_all)/500)*500;  % 向下取整到最近的500
    mass_max = 5500;   % 固定最大值为5500，显示时会除以1000显示为5.5
    xticks(mass_min:500:mass_max);

    % 安全系数轴（Y轴）：以0.05为间隔，确保显示到1.6
    safety_min = floor(min(f2_all)*20)/20;    % 向下取整到最近的0.05
    safety_max = max(1.6, ceil(max(f2_all)*20)/20);     % 确保最大值至少为1.6
    yticks(safety_min:0.05:safety_max);

    % 设置坐标轴标签 - 使用宋体
    xlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);
    ylabel('最小弯曲安全系数', 'FontName', chineseFont, 'FontSize', fontSize);

    % 使用线性坐标，显示更直观
    set(gca, 'XScale', 'linear');

    % 自定义X轴刻度标签，显示小数值
    xticks_vals = get(gca, 'XTick');
    xtick_labels = cell(size(xticks_vals));
    for i = 1:length(xticks_vals)
        xtick_labels{i} = sprintf('%.1f', xticks_vals(i)/1000);
    end
    set(gca, 'XTickLabel', xtick_labels);

    % 在X轴末端添加科学计数法标注
    xlim_vals = get(gca, 'XLim');
    ylim_vals = get(gca, 'YLim');
    text(max(xlim_vals) + (max(xlim_vals) - min(xlim_vals)) * 0.02, min(ylim_vals), '×10³', ...
         'FontName', chineseFont, 'FontSize', fontSize-1, ...
         'HorizontalAlignment', 'left', 'VerticalAlignment', 'bottom');

    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.5;         % 统一边框线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';

    % 确保标签与坐标轴平行
    ax.XLabel.Rotation = 0;  % 确保X标签水平
    ax.YLabel.Rotation = 90; % 确保Y标签垂直

    % 添加图例，放在右下角，设置为学术论文风格
    lgd = legend(legendEntries, 'Location', 'southeast', 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 调整图例中的标记大小
    end

    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end

function [varargout] = PlotMassVsContactSafety(all_results, alg_names, f1_all, f3_all)
    % 绘制质量-接触安全系数图

    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;

    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];

    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];

    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记

    % 获取当前figure
    fig = gcf;

    % 创建轴并保持
    hold on;
    grid on;
    box on;

    % 图例条目
    legendEntries = {};

    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end

        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f3 = abs(all_results{i}(:, 3));   % 接触安全系数（取绝对值确保为正）

        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;

        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f1_jitter = f1 + (rand(size(f1))-0.5)*jitter*mean(f1);
        f3_jitter = f3 + (rand(size(f3))-0.5)*jitter*mean(f3);

        % 使用散点图绘制Pareto前沿
        scatter(f1_jitter, f3_jitter, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.9, ... % 减少透明度，提高可见性
               'DisplayName', alg_names{i});

        legendEntries{end+1} = alg_names{i};
    end

    % 设置坐标轴范围，确保数据分布均匀美观
    f1_range = max(f1_all) - min(f1_all);
    f3_range = max(f3_all) - min(f3_all);

    % 使用适当的边距
    margin_percent = 0.08;  % 8%边距

    xlim([min(f1_all) - f1_range*margin_percent, 5500]);
    ylim([min(f3_all) - f3_range*margin_percent, max(f3_all) + f3_range*margin_percent]);

    % 设置合适的刻度间隔 - 质量以0.5千克为间隔，安全系数以0.1为间隔
    % 质量轴（X轴）：以0.5千克为间隔，固定最大值为5500，显示为5.5（×10³）
    mass_min = floor(min(f1_all)/500)*500;  % 向下取整到最近的500
    mass_max = 5500;   % 固定最大值为5500，显示时会除以1000显示为5.5
    xticks(mass_min:500:mass_max);

    % 安全系数轴（Y轴）：以0.05为间隔
    safety_min = floor(min(f3_all)*20)/20;    % 向下取整到最近的0.05
    safety_max = ceil(max(f3_all)*20)/20;     % 向上取整到最近的0.05
    yticks(safety_min:0.05:safety_max);

    % 设置坐标轴标签 - 使用宋体
    xlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);
    ylabel('最小接触安全系数', 'FontName', chineseFont, 'FontSize', fontSize);

    % 使用线性坐标，显示更直观
    set(gca, 'XScale', 'linear');

    % 自定义X轴刻度标签，显示小数值
    xticks_vals = get(gca, 'XTick');
    xtick_labels = cell(size(xticks_vals));
    for i = 1:length(xticks_vals)
        xtick_labels{i} = sprintf('%.1f', xticks_vals(i)/1000);
    end
    set(gca, 'XTickLabel', xtick_labels);

    % 在X轴末端添加科学计数法标注
    xlim_vals = get(gca, 'XLim');
    ylim_vals = get(gca, 'YLim');
    text(max(xlim_vals) + (max(xlim_vals) - min(xlim_vals)) * 0.02, min(ylim_vals), '×10³', ...
         'FontName', chineseFont, 'FontSize', fontSize-1, ...
         'HorizontalAlignment', 'left', 'VerticalAlignment', 'bottom');

    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.5;         % 统一边框线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';

    % 确保标签与坐标轴平行
    ax.XLabel.Rotation = 0;  % 确保X标签水平
    ax.YLabel.Rotation = 90; % 确保Y标签垂直

    % 添加图例，放在右下角，设置为学术论文风格
    lgd = legend(legendEntries, 'Location', 'southeast', 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 调整图例中的标记大小
    end

    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end
