function [varargout] = PlotRadarMetrics(all_results, alg_names, save_path, computation_times)
% PLOTRADARMETRICS 绘制算法多指标雷达图
% 根据原有Metrics文件夹的评估方法生成雷达图
%
% 输入:
%   all_results - 包含各算法结果的cell数组，每个元素是一个N×3的矩阵
%   alg_names - 算法名称数组
%   save_path - 可选，保存图像的路径
%   computation_times - 可选，真实的计算时间数组
%
% 输出:
%   varargout{1} - 可选，返回图窗句柄

% 设置默认字体为宋体
set(0, 'DefaultAxesFontName', 'SimSun');
set(0, 'DefaultTextFontName', 'SimSun');
set(0, 'DefaultUicontrolFontName', 'SimSun');

% 如果没有提供保存路径，设置默认值
if nargin < 3
    save_path = '';
end

% 如果没有提供计算时间，设置默认值
if nargin < 4 || isempty(computation_times)
    computation_times = [];
end

% 使用EvaluateAlgorithms函数计算性能指标
fprintf('正在计算算法性能指标用于雷达图...\n');
try
    metrics = EvaluateAlgorithms(all_results, alg_names, computation_times);
    fprintf('✓ 性能指标计算完成\n');
catch e
    fprintf('计算性能指标时出错: %s\n', e.message);
    % 创建默认的metrics结构体
    metrics = struct();
    metrics.GD = ones(1, length(alg_names));
    metrics.IGD = ones(1, length(alg_names));
    metrics.Spread = ones(1, length(alg_names));
    metrics.HV = ones(1, length(alg_names));
    metrics.Coverage = ones(1, length(alg_names));
    metrics.Time = ones(1, length(alg_names));
    metrics.AlgorithmNames = alg_names;
end

% 确保算法名称匹配
if isfield(metrics, 'AlgorithmNames')
    % 使用metrics中的算法名称
    alg_names = metrics.AlgorithmNames;
end

% 获取有效的算法数量
n_algs = length(alg_names);

% 提取我们需要的六个指标
indicator_names = {'GD', 'IGD', 'Spread', 'HV', 'Coverage', 'Time'};
n_indicators = length(indicator_names);

% 创建数据矩阵
data = zeros(n_algs, n_indicators);

% 填充数据矩阵
for i = 1:n_algs
    for j = 1:n_indicators
        field_name = indicator_names{j};
        if isfield(metrics, field_name) && i <= length(metrics.(field_name))
            data(i, j) = metrics.(field_name)(i);
        end
    end
end

% 标准化数据到0.1-1.0范围，考虑指标方向性
norm_data = zeros(size(data));

% 定义指标方向：true表示越大越好，false表示越小越好
% 顺序：GD, IGD, Spread, HV, Coverage, Time
indicator_directions = [false, false, false, true, true, false];

% 为了雷达图显示，需要对数据进行适当的缩放
% 同时考虑指标的优化方向
for j = 1:n_indicators
    indicator_data = data(:, j);

    % 跳过全零的指标
    if all(indicator_data == 0)
        norm_data(:, j) = 0.1; % 全零设为最差性能
        continue;
    end

    % 过滤有效数据
    valid_data = indicator_data(indicator_data > 0);
    if isempty(valid_data)
        norm_data(:, j) = 0.1;
        continue;
    end

    min_val = min(valid_data);
    max_val = max(valid_data);

    if max_val > min_val
        if indicator_directions(j)
            % 越大越好的指标(HV, Coverage)：大值映射到外圈(1.0)
            norm_data(:, j) = 0.1 + 0.9 * (indicator_data - min_val) / (max_val - min_val);
        else
            % 越小越好的指标(GD, IGD, Spread, Time)：小值映射到外圈(1.0)
            norm_data(:, j) = 1.0 - 0.9 * (indicator_data - min_val) / (max_val - min_val) + 0.1;
        end

        % 处理0值（通常表示算法失败或无效）
        zero_indices = indicator_data == 0;
        if any(zero_indices)
            norm_data(zero_indices, j) = 0.1; % 0值设为最差性能
        end
    else
        % 如果所有值相同，设为中间值
        norm_data(:, j) = 0.5;
    end
end

% 创建图窗 - 调整为合适的尺寸并居中显示
screen_size = get(0, 'ScreenSize');
% 设置3×3布局专用尺寸
fig_width = min(1200, screen_size(3) * 0.8);
fig_height = min(1000, screen_size(4) * 0.8);
fig_x = (screen_size(3) - fig_width) / 2;
fig_y = (screen_size(4) - fig_height) / 2;
fig = figure('Position', [fig_x, fig_y, fig_width, fig_height], ...
    'Name', '算法性能雷达图 (3×3布局)', 'NumberTitle', 'off', ...
    'Color', 'white', 'MenuBar', 'figure', 'ToolBar', 'figure', ...
    'Resize', 'on');

% 设置鲜艳的颜色方案 - 与前沿图保持一致
colors = [
    1.0000, 0.0000, 0.0000;  % 红色 - NSGA-II
    0.0000, 0.0000, 1.0000;  % 蓝色 - NSGA-III
    0.0000, 0.7000, 0.0000;  % 鲜绿色 - SPEA2
    0.8000, 0.0000, 0.8000;  % 亮紫色 - MOEA-D
    0.0000, 0.8000, 0.8000;  % 青绿色 - MOEA-D-DE
    1.0000, 0.6000, 0.0000;  % 橙色 - MOEA-D-M2M
    0.5000, 0.0000, 1.0000;  % 紫色 - MOPSO
    1.0000, 0.9500, 0.0000;  % 亮黄色 - MOGWO
    1.0000, 0.0000, 0.5000;  % 品红色 - MOWOA
];

% 确保颜色数量足够
if n_algs > size(colors, 1)
    colors = [colors; jet(n_algs - size(colors, 1))];
end

% 计算雷达图的角度
theta = linspace(0, 2*pi, n_indicators+1);
theta = theta(1:end-1); % 移除最后一个重复的点

% 固定使用3×3布局，确保对齐
rows = 3;
cols = 3;

% 添加总标题 - 使用宋体
title_str = sprintf('多目标优化算法性能雷达图对比 (共%d个算法)', min(n_algs, 9));
sgtitle(title_str, 'FontSize', 16, 'FontWeight', 'bold', 'FontName', 'SimSun', 'Color', [0.1 0.1 0.1]);

% 绘制每个算法的雷达图到独立子图
max_display = min(n_algs, rows*cols);
for i = 1:max_display
    % 创建子图 - 使用精确的位置控制
    subplot(rows, cols, i);

    % 获取当前子图的位置并调整 - 整体下移
    pos = get(gca, 'Position');
    % 调整子图位置，整体下移并增加与标题的间距
    pos(1) = pos(1) + 0.02; % 左边距调整
    pos(2) = pos(2) - 0.05; % 下边距调整（下移更多）
    pos(3) = pos(3) - 0.04; % 宽度调整
    pos(4) = pos(4) - 0.02; % 高度调整（减少高度压缩）
    set(gca, 'Position', pos);

    % 初始化子图设置
    hold on;
    axis equal;
    axis off;

    % 设置统一的坐标轴范围 - 调小范围使雷达图更大
    xlim([-1.3, 1.3]);
    ylim([-1.3, 1.3]);

    % 绘制背景网格 - 黑色密虚线六边形网格
    for k = 1:5
        r = k * 0.2;
        % 绘制六边形而不是圆形
        hex_angles = linspace(0, 2*pi, 7); % 7个点形成6边形（最后一个点与第一个重合）
        hex_x = r * cos(hex_angles);
        hex_y = r * sin(hex_angles);
        plot(hex_x, hex_y, 'Color', [0 0 0], 'LineStyle', ':', 'LineWidth', 0.5);
    end

    % 绘制径向线 - 黑色密虚线
    for j = 1:n_indicators
        line([0, 1.05*cos(theta(j))], [0, 1.05*sin(theta(j))], ...
            'Color', [0 0 0], 'LineStyle', ':', 'LineWidth', 0.7);
    end

    % 不显示刻度标签 - 保持简洁的六边形网格
    
    % 添加指标标签 - 与雷达图角度完全对应
    for j = 1:n_indicators
        % 使用与雷达图数据点相同的角度计算方式
        angle = theta(j); % 直接使用雷达图的角度
        label_distance = 1.4;
        label_x = label_distance * cos(angle);
        label_y = label_distance * sin(angle);

        % 计算标签底部朝外的角度（度数）
        rotation_angle = angle * 180 / pi - 90; % 文字底部朝向外侧

        % 确保角度在合理范围内，避免倒置文字
        if rotation_angle < -90 || rotation_angle > 90
            rotation_angle = rotation_angle + 180; % 翻转180度避免倒置
        end

        % 添加指标标签 - 保持半透明背景风格
        text(label_x, label_y, indicator_names{j}, ...
            'HorizontalAlignment', 'center', ...
            'VerticalAlignment', 'middle', ...
            'FontSize', 10, ...                  % 适中字体大小
            'FontWeight', 'bold', ...            % 粗体确保清晰
            'FontName', 'Times New Roman', ...   % 科研文章标准字体
            'Color', [0.2 0.2 0.2], ...         % 深色文字
            'BackgroundColor', [1 1 1 0.7], ... % 半透明背景确保可读
            'EdgeColor', 'none', ...             % 无边框
            'Rotation', rotation_angle);
    end
    
    % 闭合多边形的数据
    values = [norm_data(i, :), norm_data(i, 1)];
    angles = [theta, theta(1)];
    
    % 转换为笛卡尔坐标
    x = values .* cos(angles);
    y = values .* sin(angles);
    
    % 绘制多边形 - 调小线条粗细
    patch(x, y, colors(i,:), 'FaceAlpha', 0.3, 'EdgeColor', colors(i,:), 'LineWidth', 1.5);

    % 添加数据点 - 调小圆圈大小
    scatter(x(1:end-1), y(1:end-1), 28, colors(i,:), 'filled', ...
        'MarkerEdgeColor', [1 1 1], 'LineWidth', 1);

    % 添加数据点阴影效果 - 同步调小
    scatter(x(1:end-1)+0.02, y(1:end-1)-0.02, 28, [0.2 0.2 0.2], ...
        'filled', 'MarkerFaceAlpha', 0.2);

    % 添加算法名称作为子图标题 - 使用Times New Roman
    title(alg_names{i}, 'FontSize', 12, 'Color', [0.1 0.1 0.1], 'FontWeight', 'bold', ...
        'FontName', 'Times New Roman', 'Position', [0, 1.75, 0], 'HorizontalAlignment', 'center');

    % 设置统一的轴范围 - 调小范围使雷达图更大
    axis([-1.3 1.3 -1.3 1.3]);
    axis equal;
    axis off;
end

% 调整整体布局
set(fig, 'Units', 'normalized');

% 显示性能指标数值表格
displayMetricsTable(metrics, alg_names);

% 计算并显示综合性能排名
calculateOverallRanking(metrics, alg_names);

% 如果提供了保存路径，则保存图像
if nargin >= 3 && ~isempty(save_path)
    try
        % 使用print函数保存，更适合批处理模式
        [path_dir, ~, ~] = fileparts(save_path);
        if ~exist(path_dir, 'dir')
            mkdir(path_dir);
        end

        % 保存为PNG格式
        print(fig, save_path, '-dpng', '-r300');
        fprintf('雷达图已保存至: %s\n', save_path);

        % 同时保存性能指标表格到Excel文件
        saveMetricsToExcel(metrics, alg_names, path_dir);

    catch e
        fprintf('保存雷达图失败: %s\n', e.message);
        % 尝试使用saveas作为备选方案
        try
            saveas(fig, save_path);
            fprintf('雷达图已保存至: %s (使用备选方案)\n', save_path);
        catch e2
            fprintf('备选保存方案也失败: %s\n', e2.message);
        end
    end
end

% 返回图窗句柄，方便调用者进一步处理
if nargout > 0
    varargout{1} = fig;
end
end

function displayMetricsTable(metrics, alg_names)
% 显示性能指标数值表格
fprintf('\n=== 算法性能指标详细数值 ===\n');

% 首先计算综合排名
overall_ranking = calculateOverallRankingValues(metrics, alg_names);

fprintf('%-12s %8s %8s %8s %8s %8s %8s %8s\n', '算法', 'HV', 'GD', 'IGD', 'Spread', 'Coverage', 'Time(s)', '综合排名');
fprintf('%s\n', repmat('-', 1, 78));

for i = 1:length(alg_names)
    % 处理Coverage指标 - 如果是矩阵，计算平均值
    coverage_value = 0;
    if isfield(metrics, 'Coverage') && ~isempty(metrics.Coverage)
        if size(metrics.Coverage, 1) > 1 && size(metrics.Coverage, 2) > 1
            % Coverage是矩阵，计算该算法对其他算法的平均覆盖率
            coverage_row = metrics.Coverage(i, :);
            coverage_row(i) = []; % 移除自己对自己的覆盖率
            coverage_value = mean(coverage_row(~isnan(coverage_row)));
        else
            % Coverage是向量
            coverage_value = metrics.Coverage(i);
        end
    end

    fprintf('%-12s %8.4f %8.4f %8.4f %8.4f %8.4f %8.2f %8d\n', ...
        alg_names{i}, ...
        metrics.HV(i), ...
        metrics.GD(i), ...
        metrics.IGD(i), ...
        metrics.Spread(i), ...
        coverage_value, ...
        metrics.Time(i), ...
        overall_ranking(i));
end

fprintf('\n指标说明:\n');
fprintf('  GD (Generation Distance): 生成距离，越小越好\n');
fprintf('  IGD (Inverted Generation Distance): 反向生成距离，越小越好\n');
fprintf('  Spread: 分布性指标，越小越好（分布越均匀）\n');
fprintf('  HV (Hypervolume): 超体积，越大越好\n');
fprintf('  Coverage: 覆盖率，越大越好（算法解集支配其他算法解集的程度）\n');
fprintf('  Time: 计算时间，越小越好\n');
fprintf('  综合排名: 基于所有指标的综合排名，数值越小排名越高\n\n');
end

function saveMetricsToExcel(metrics, alg_names, save_dir)
% 保存性能指标到Excel文件
try
    % 创建表格数据
    table_data = table();
    table_data.Algorithm = alg_names';
    table_data.HV = metrics.HV';
    table_data.GD = metrics.GD';
    table_data.IGD = metrics.IGD';
    table_data.Spread = metrics.Spread';

    % 处理Coverage指标
    coverage_values = zeros(length(alg_names), 1);
    if isfield(metrics, 'Coverage') && ~isempty(metrics.Coverage)
        for i = 1:length(alg_names)
            if size(metrics.Coverage, 1) > 1 && size(metrics.Coverage, 2) > 1
                % Coverage是矩阵，计算该算法对其他算法的平均覆盖率
                coverage_row = metrics.Coverage(i, :);
                coverage_row(i) = []; % 移除自己对自己的覆盖率
                coverage_values(i) = mean(coverage_row(~isnan(coverage_row)));
            else
                % Coverage是向量
                coverage_values(i) = metrics.Coverage(i);
            end
        end
    end
    table_data.Coverage = coverage_values;
    table_data.Time = metrics.Time';

    % 添加综合排名
    overall_ranking = calculateOverallRankingValues(metrics, alg_names);
    table_data.OverallRanking = overall_ranking;

    % 保存到Excel文件
    excel_file = fullfile(save_dir, '算法性能指标详细表.xlsx');
    writetable(table_data, excel_file);
    fprintf('性能指标详细表已保存至: %s\n', excel_file);

catch e
    fprintf('保存性能指标Excel文件失败: %s\n', e.message);
end
end

function overall_ranking = calculateOverallRankingValues(metrics, alg_names)
% 计算综合性能排名值（返回每个算法的排名）
n_algs = length(alg_names);
if n_algs == 0
    overall_ranking = [];
    return;
end

% 对每个指标进行排名（1=最好，n=最差）
rankings = zeros(n_algs, 6);

% GD: 越小越好
[~, idx] = sort(metrics.GD);
rankings(idx, 1) = 1:n_algs;

% IGD: 越小越好
[~, idx] = sort(metrics.IGD);
rankings(idx, 2) = 1:n_algs;

% Spread: 越小越好
[~, idx] = sort(metrics.Spread);
rankings(idx, 3) = 1:n_algs;

% HV: 越大越好
[~, idx] = sort(metrics.HV, 'descend');
rankings(idx, 4) = 1:n_algs;

% Coverage: 越大越好
coverage_values = zeros(1, n_algs);
if isfield(metrics, 'Coverage') && ~isempty(metrics.Coverage)
    for i = 1:n_algs
        if size(metrics.Coverage, 1) > 1 && size(metrics.Coverage, 2) > 1
            % Coverage是矩阵，计算该算法对其他算法的平均覆盖率
            coverage_row = metrics.Coverage(i, :);
            coverage_row(i) = []; % 移除自己对自己的覆盖率
            coverage_values(i) = mean(coverage_row(~isnan(coverage_row)));
        else
            % Coverage是向量
            coverage_values(i) = metrics.Coverage(i);
        end
    end
end
[~, idx] = sort(coverage_values, 'descend');
rankings(idx, 5) = 1:n_algs;

% Time: 越小越好
[~, idx] = sort(metrics.Time);
rankings(idx, 6) = 1:n_algs;

% 计算平均排名
avg_ranking = mean(rankings, 2);

% 按平均排名排序，得到最终排名
[~, sort_idx] = sort(avg_ranking);
overall_ranking = zeros(n_algs, 1);
for i = 1:n_algs
    overall_ranking(sort_idx(i)) = i;
end
end

function calculateOverallRanking(metrics, alg_names)
% 计算综合性能排名
fprintf('\n=== 算法综合性能排名 ===\n');

n_algs = length(alg_names);
if n_algs == 0
    fprintf('没有算法数据可供排名\n');
    return;
end

% 对每个指标进行排名（1=最好，n=最差）
rankings = zeros(n_algs, 6);

% GD: 越小越好
[~, idx] = sort(metrics.GD);
rankings(idx, 1) = 1:n_algs;

% IGD: 越小越好
[~, idx] = sort(metrics.IGD);
rankings(idx, 2) = 1:n_algs;

% Spread: 越小越好
[~, idx] = sort(metrics.Spread);
rankings(idx, 3) = 1:n_algs;

% HV: 越大越好
[~, idx] = sort(metrics.HV, 'descend');
rankings(idx, 4) = 1:n_algs;

% Coverage: 越大越好
coverage_values = zeros(1, n_algs);
if isfield(metrics, 'Coverage') && ~isempty(metrics.Coverage)
    for i = 1:n_algs
        if size(metrics.Coverage, 1) > 1 && size(metrics.Coverage, 2) > 1
            % Coverage是矩阵，计算该算法对其他算法的平均覆盖率
            coverage_row = metrics.Coverage(i, :);
            coverage_row(i) = []; % 移除自己对自己的覆盖率
            coverage_values(i) = mean(coverage_row(~isnan(coverage_row)));
        else
            % Coverage是向量
            coverage_values(i) = metrics.Coverage(i);
        end
    end
end
[~, idx] = sort(coverage_values, 'descend');
rankings(idx, 5) = 1:n_algs;

% Time: 越小越好
[~, idx] = sort(metrics.Time);
rankings(idx, 6) = 1:n_algs;

% 计算平均排名
avg_ranking = mean(rankings, 2);

% 按平均排名排序
[sorted_avg_ranking, sort_idx] = sort(avg_ranking);

fprintf('%-12s %8s %8s %8s %8s %8s %8s %10s\n', ...
    '算法', 'GD排名', 'IGD排名', 'Spread排名', 'HV排名', 'Coverage排名', 'Time排名', '平均排名');
fprintf('%s\n', repmat('-', 1, 85));

for i = 1:n_algs
    alg_idx = sort_idx(i);
    fprintf('%-12s %8d %8d %8d %8d %8d %8d %10.2f\n', ...
        alg_names{alg_idx}, ...
        rankings(alg_idx, 1), ...
        rankings(alg_idx, 2), ...
        rankings(alg_idx, 3), ...
        rankings(alg_idx, 4), ...
        rankings(alg_idx, 5), ...
        rankings(alg_idx, 6), ...
        sorted_avg_ranking(i));
end

fprintf('\n综合性能前三名:\n');
for i = 1:min(3, n_algs)
    alg_idx = sort_idx(i);
    fprintf('  第%d名: %s (平均排名: %.2f)\n', i, alg_names{alg_idx}, sorted_avg_ranking(i));
end
fprintf('\n');
end
